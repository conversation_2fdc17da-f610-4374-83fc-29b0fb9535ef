function getBANOIYHCTJSON () {
    var form;
    var formTongket;
    var formPage1;
    var keyMauHSBANOITRUYHCT = "MAUHSBANOITRUYHCT";
    var keyMauHSBANOITRUYHCTTongket = "MAUHSBANOITRUYHCTTONGKET";
    var formioMauHSBA;
    var formioMauHSBATongket;
    return {
        script: {},
        scriptTongket: {},
        initObjectFormPage1: function(edit, hidden) {
            return getJSONObjectForm([
                {
                    "key": "p-chandoan",
                    "type": "tabs",
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "QUẢN LÝ NGƯỜI BỆNH",
                            "key": "tabQuanLyNguoiBenh",
                            "components": [
                                getObjectQuanLyNguoiBenhVBAT1_1(edit, hidden),
                            ]
                        },
                        {
                            "label": "CHẨN ĐOÁN",
                            "key": "tabChanDoan",
                            "components": [
                                getObjectChanDoanVBAT1_3(edit, hidden),
                            ]
                        },
                        {
                            "label": "TÌNH TRẠNG RA VIỆN",
                            "key": "tabTinhTrangRaVien",
                            "components": [
                                getObjectTinhTrangRaVienVBAT1_2(edit, hidden),
                            ]
                        },
                    ]
                },
            ])
        },
        initObjectFormPage2: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "A. Y HỌC HIỆN ĐẠI",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "BỆNH ÁN VÀ HỎI BỆNH",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    label: "lydovaovien",
                                    key: "colLydovaovien",
                                    columns: [
                                        {
                                            "components": [
                                                {
                                                    "label": "Lý do vào viện",
                                                    "key": "LYDOVAOVIEN",
                                                    "type": "textarea",
                                                    customClass: "pr-2",
                                                    rows: 2,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 9,
                                            "size": "md",
                                        },
                                        {
                                            "components": [

                                                {
                                                    "label": "Vào ngày thứ",
                                                    "key": "VAONGAYTHU",
                                                    "type": "number",
                                                    "validate": {
                                                        "min": 1,
                                                        max: 200,
                                                    }
                                                },
                                            ],
                                            "width": 3,
                                            "size": "md",
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "type": "columns",
                                },
                                {
                                    "label": "Bệnh sử",
                                    others: {
                                        "tooltip": "Quá trình bệnh lý: (khởi phát, diễn biến, chẩn đoán, điều trị tuyến dưới, v.v...)",
                                    },
                                    "key": "BENHSU",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Tiền sử bệnh (bản thân)",
                                    others: {
                                        "tooltip": " Bản thân: (phát triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)",
                                    },
                                    "key": "TIENSUBANTHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": " ",
                                    "validate": {
                                        "custom": "if (data.TIENSU.length > 4) {\r\n    valid = \"Không thể chọn quá 4 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                    },
                                    "key": "TIENSU",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "1. Dị ứng",
                                                    "value": "1"
                                                },
                                                {
                                                    "label": "2. Rượu",
                                                    "value": "2"
                                                },
                                                {
                                                    "label": "3. Ma túy",
                                                    "value": "3"
                                                },
                                                {
                                                    "label": "4. Thuốc lá",
                                                    "value": "4"
                                                },
                                                {
                                                    "label": "5. Khác",
                                                    "value": "5"
                                                }
                                            ]
                                        },
                                        "input": true,
                                        "widget": "choicesjs",
                                        "tableView": true,
                                        "multiple": true,
                                    },
                                    "type": "select",
                                },
                                {
                                    "label": "Mô tả (nếu có)",
                                    "key": "MOTABANTHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Đặc điểm liên quan đến bệnh tật",
                                    "key": "TIENSUBENHTAT",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Tiền sử bệnh (gia đình)",
                                    "key": "TIENSUGIADINH",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-khambenh",
                            "type": "panel",
                            "label": "Khám bệnh",
                            "title": "KHÁM BỆNH",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Khám toàn thân",
                                                    "others": {
                                                        "tooltip": " Toàn thân: (ý thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích thước)",
                                                    },
                                                    "key": "KHAMTOANTHAN",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Tuần hoàn",
                                                    "key": "TUANHOAN",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Hô hấp",
                                                    "key": "HOHAP",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Tiêu hóa",
                                                    "key": "TIEUHOA",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                }
                                            ],
                                            "width": 7,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 7
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Tabs",
                                                    "components": [
                                                        {
                                                            "label": "Chỉ số sinh tồn",
                                                            "key": "sinhhieu",
                                                            "components": [
                                                                {
                                                                    "label": "chisosinhton1",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Mạch",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "MACH",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Nhiệt độ",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 35,
                                                                                        "max": 43,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "NHIETDO",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Nhịp thở",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "NHIPTHO",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "size": "md",
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "currentWidth": 4
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton1",
                                                                    "type": "columns",
                                                                },
                                                                {
                                                                    "label": "chisosinhton2",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Cân nặng (kg)",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 0,
                                                                                        "max": 400,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "CANNANG",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Chiều cao (cm)",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 1,
                                                                                        "max": 400,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "CHIEUCAO",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "BMI",
                                                                                    "key": "BMI",
                                                                                    others: {
                                                                                        "disabled": true,
                                                                                        "attributes": {
                                                                                            "readonly": "true"
                                                                                        },
                                                                                    },
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "size": "md",
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "currentWidth": 4
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton2",
                                                                    "type": "columns",
                                                                },
                                                                {
                                                                    "label": "chisosinhton3",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "tag": "label",
                                                                                    "content": "Huyết áp",
                                                                                    "refreshOnChange": false,
                                                                                    "key": "htmllabel_huyetap",
                                                                                    "type": "htmlelement",
                                                                                },
                                                                            ],
                                                                            "width": 12,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "HUYETAPTREN",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 6,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "HUYETAPDUOI",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 6,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 6
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton3",
                                                                    "type": "columns",
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    "customClass": "hsba-tabs-wrap pl-3",
                                                    "key": "tabs",
                                                    "type": "tabs",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            customClass: "pl-2",
                                            "currentWidth": 5
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Tiết niệu - Sinh dục",
                                                    "key": "TIETNIEUSINHDUC",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Thần kinh",
                                                    "key": "THANKINH",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Cơ - Xương - Khớp",
                                                    "key": "XUONGKHOP",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Tai - Mũi - Họng",
                                                    "key": "TMH",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Răng - Hàm - Mặt",
                                                    "key": "RHM",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Mắt",
                                                    "key": "MAT",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "Nội tiết, dinh dưỡng và các bệnh lý khác",
                                    "key": "NOITIET",
                                    "type": "textarea",
                                    "rows": 1,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Copy cận lâm sàng",
                                    "customClass": "text-right form-control-sm line-height-1",
                                    "key": "copyclstdt",
                                    "type": "button",
                                    others: {
                                        "leftIcon": "fa fa-ellipsis-v",
                                        "action": "event",
                                        "showValidations": false,
                                        "event": "openmodalcopycls",
                                        "type": "button",
                                    }
                                },
                                {
                                    "label": "Các xét nghiệm cận lâm sàng cần làm",
                                    "key": "CLS",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Tóm tắt bệnh án",
                                    "key": "TOMTATBA",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                }
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-chandoanvadieutri",
                            "type": "panel",
                            "label": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
                            "title": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    label: "",
                                    key: "wrap_benhchinh",
                                    columns: [
                                        {
                                            "components": [
                                                {
                                                    "tag": "label",
                                                    "content": "Bệnh chính",
                                                    "refreshOnChange": false,
                                                    "key": "htmllabel_benhchinh",
                                                    "type": "htmlelement",
                                                },
                                            ],
                                            "width": 12,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "ICD_BENHCHINH",
                                                    "type": "textfield",
                                                    customClass: "pr-2",
                                                    others: {
                                                        "placeholder": "ICD",
                                                    }
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "TENICD_BENHCHINH",
                                                    "type": "textfield",
                                                    others: {
                                                        "placeholder": "Tên bệnh chính",
                                                    },
                                                    validate: {
                                                        required: true
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 8,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "ICDCHINH1",
                                                    "type": "textfield",
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "type": "columns",
                                },
                                {
                                    label: "",
                                    key: "wrap_benhphu",
                                    columns: [
                                        {
                                            "components": [
                                                {
                                                    "tag": "label",
                                                    "attrs": [
                                                        {
                                                            "attr": "",
                                                            "value": ""
                                                        }
                                                    ],
                                                    "content": "Bệnh kèm theo (nếu có)",
                                                    "key": "htmllabel_benhphu",
                                                    "type": "htmlelement",
                                                },
                                            ],
                                            "width": 12,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "ICD_BENHPHU",
                                                    "type": "textfield",
                                                    customClass: "pr-2",
                                                    others: {
                                                        "placeholder": "ICD",
                                                    }
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "TENICD_BENHPHU",
                                                    "type": "textfield",
                                                    others: {
                                                        "placeholder": "Tên bệnh",
                                                    }
                                                },
                                            ],
                                            "width": 10,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "TENBENHPHU1",
                                                    "type": "textarea",
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    "rows": 2,
                                                    "input": true,
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 10,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "ICDPHU1",
                                                    "type": "textfield",
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "type": "columns",
                                },
                                {
                                    "label": "Phân biệt",
                                    "key": "PHANBIET",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    "rows": 2,
                                },
                            ]
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "B. Y HỌC CỔ TRUYỀN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "I. VỌNG CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1. Hình thái",
                                                    "validate": {
                                                        "custom": "if (data.HINHTHAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HINHTHAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Gầy",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Béo",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Cân đối",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Nằm co",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Nằm duỗi",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Ưa tĩnh",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Ưa động",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                        customClass: "pr-2",
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "2. Thần",
                                                    "validate": {
                                                        "custom": "if (data.THAN.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "THAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Còn thần",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không còn thần",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khác",
                                                                    "value": "3"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "3. Sắc",
                                                    "validate": {
                                                        "custom": "if (data.SAC.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "SAC",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Bệch (trắng)",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đỏ",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Vàng",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Xanh",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Đen",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Bình thường",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Khác",
                                                                    "value": "7"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                        customClass: "pr-2",
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "4. Trạch",
                                                    "validate": {
                                                        "custom": "if (data.TRACH.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TRACH",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Tươi nhuận",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Khô",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khác",
                                                                    "value": "3"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "5.1 Hình thái lưỡi",
                                                    "validate": {
                                                        "custom": "if (data.HINHLUOI.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HINHLUOI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Bình thường",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Lệch",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Rụt",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                        customClass: "pr-2",
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "5.2 Chất lưỡi, sắc lưỡi",
                                                    "validate": {
                                                        "custom": "if (data.CHATLUOI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "CHATLUOI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Đạm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Bệu",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Gầy mỏng",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Nứt",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Cứng",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Loét",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Hồng",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Nhợt",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. Đỏ",
                                                                    "value": "9"
                                                                },
                                                                {
                                                                    "label": "10. Đỏ sẫm",
                                                                    "value": "10"
                                                                },
                                                                {
                                                                    "label": "11. Tím",
                                                                    "value": "11"
                                                                },
                                                                {
                                                                    "label": "12. Khác",
                                                                    "value": "12"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "5.3 Rêu lưỡi",
                                    "validate": {
                                        "custom": "if (data.REULUOI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                    },
                                    "key": "REULUOI",
                                    others : {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "1. Có",
                                                    "value": "1"
                                                },
                                                {
                                                    "label": "2. Không",
                                                    "value": "2"
                                                },
                                                {
                                                    "label": "3. Bong",
                                                    "value": "3"
                                                },
                                                {
                                                    "label": "4. Dầy",
                                                    "value": "4"
                                                },
                                                {
                                                    "label": "5. Mỏng",
                                                    "value": "5"
                                                },
                                                {
                                                    "label": "6. Ướt",
                                                    "value": "6"
                                                },
                                                {
                                                    "label": "7. Khô",
                                                    "value": "7"
                                                },
                                                {
                                                    "label": "8. Nhuận",
                                                    "value": "8"
                                                },
                                                {
                                                    "label": "9. Dính",
                                                    "value": "9"
                                                },
                                                {
                                                    "label": "10. Trắng",
                                                    "value": "10"
                                                },
                                                {
                                                    "label": "11. Vàng",
                                                    "value": "11"
                                                },
                                                {
                                                    "label": "12. Khác",
                                                    "value": "12"
                                                },
                                            ]
                                        },
                                        "input": true,
                                        "widget": "choicesjs",
                                        "tableView": true,
                                        "multiple": true,
                                    },
                                    "type": "select",
                                },
                                {
                                    "label": "Mô tả khác (nếu có)",
                                    "key": "MOTAVONGCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    "rows": 2,
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "II. VĂN CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1.1. Tiếng nói",
                                                    "validate": {
                                                        "custom": "if (data.TIENGNOI.length > 5) {\r\n    valid = \"Không thể chọn quá 5 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TIENGNOI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Bình thường",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. To",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Nhỏ",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Đứt quãng",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khàn",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Ngọng",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Mất",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "1.2. Hơi thở",
                                                    "validate": {
                                                        "custom": "if (data.HOITHO.length > 5) {\r\n    valid = \"Không thể chọn quá 5 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HOITHO",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Bình thường",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đứt quãng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Ngắn",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Mạnh",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Yếu",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Thô",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Rít",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khò khè",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. Chậm",
                                                                    "value": "9"
                                                                },
                                                                {
                                                                    "label": "10. Gấp",
                                                                    "value": "10"
                                                                },
                                                                {
                                                                    "label": "11. Khác",
                                                                    "value": "11"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1.3. Ho",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COHO",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.HO.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HO",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Ho liên tục",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Cơn",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Ít",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Nhiều",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khan",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Có đờm",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Khác",
                                                                    "value": "7"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1.4. Ợ",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COO",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "1.5. Nấc",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "CONAC",
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "2.1. Mùi cơ thể, mùi hơi thở",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COMUI",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.MUICOTHE.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "MUICOTHE",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Chua",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Khắm",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Tanh",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Thối",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hôi",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Khác",
                                                                    "value": "6"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "2.2. Chất thải biểu hiện bệnh lý",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COCHATTHAI",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.CHATTHAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "CHATTHAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Đờm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Chất nôn",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phân",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Nước tiểu",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khí hư",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Kinh nguyệt",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Khác",
                                                                    "value": "7"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "3. Mô tả khác (nếu có)",
                                    "key": "MOTAVANCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    "rows": 2,
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "III. VẤN CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1. Hàn nhiệt",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COHANNHIET",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.HANNHIET.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HANNHIET",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Thích nóng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Sợ nóng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Thích mát",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sợ lạnh",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Trong người nóng",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Trong người lạnh",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Rét run",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Hàn nhiệt vãng lai",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. Khác",
                                                                    "value": "9"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "2. Mồ hôi",
                                    "validate": {
                                        "custom": "if (data.MOHOI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                    },
                                    "key": "MOHOI",
                                    others : {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "1. Bình thường",
                                                    "value": "1"
                                                },
                                                {
                                                    "label": "2. Không có mồ hôi",
                                                    "value": "2"
                                                },
                                                {
                                                    "label": "3. Tự hãn",
                                                    "value": "3"
                                                },
                                                {
                                                    "label": "4. Đạo hãn",
                                                    "value": "4"
                                                },
                                                {
                                                    "label": "5. Nhiều",
                                                    "value": "5"
                                                },
                                                {
                                                    "label": "6. Ít",
                                                    "value": "6"
                                                },
                                                {
                                                    "label": "7. Khác",
                                                    "value": "7"
                                                },
                                            ]
                                        },
                                        "input": true,
                                        "widget": "choicesjs",
                                        "tableView": true,
                                        "multiple": true,
                                    },
                                    "type": "select",
                                },
                                {
                                    "label": "3. Đầu mặt cổ",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "1. Có",
                                                    "value": "1"
                                                },
                                                {
                                                    "label": "2. Không",
                                                    "value": "2"
                                                },
                                            ]
                                        },
                                    },
                                    key: "CODAUMATCO",
                                    "type": "select",
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "3.1. Đau đầu",
                                                    "validate": {
                                                        "custom": "if (data.DAUDAU.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "DAUDAU",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Một chỗ",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Nửa đầu",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Cả đầu",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Di chuyển",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Ê ẩm",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Nhói",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Căng",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Nặng đầu",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. Đau thắt lưng",
                                                                    "value": "9"
                                                                },
                                                                {
                                                                    "label": "10. Khác",
                                                                    "value": "10"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "3.2. Hoa mắt chóng mặt",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COHMCM",
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "3.3. Mắt",
                                                    "validate": {
                                                        "custom": "if (data.DOIMAT.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "DOIMAT",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Nhìn không rõ",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đau",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khác",
                                                                    "value": "3"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "3.4. Tai",
                                                    "validate": {
                                                        "custom": "if (data.TAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Ù",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Điếc",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Đau",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "3.5. Mũi",
                                                    "validate": {
                                                        "custom": "if (data.MUI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "MUI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Ngạt",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Chảy nước",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Chảy máu cam",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Đau",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "3.6. Họng",
                                                    "validate": {
                                                        "custom": "if (data.HONG.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "HONG",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Đau",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Khô",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khác",
                                                                    "value": "3"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "3.7. Cổ vai",
                                                    "validate": {
                                                        "custom": "if (data.COVAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "COVAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Mỏi",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đau",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khó vận động",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "4. Lưng",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COLUNG",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Mỏi lưng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đau lưng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Khó vận động",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "LUNG",
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "5. Ngực",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "CONGUC",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.NGUC.length > 6) {\r\n    valid = \"Không thể chọn quá 6 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "NGUC",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Tức",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đau",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Ngột ngạt khó thở",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Đau tức cạnh sườn",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Đánh trống ngực",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Khác",
                                                                    "value": "6"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "6. Bụng",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COBUNG",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.BUNG.length > 6) {\r\n    valid = \"Không thể chọn quá 6 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "BUNG",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Đau",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Sôi bụng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Nóng ruột",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Đầy trướng",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "7. Chân tay",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COCHANTAY",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.CHANTAY.length > 6) {\r\n    valid = \"Không thể chọn quá 6 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "CHANTAY",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Đau",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Tê",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Buồn",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Mỏi",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Nhức",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Nóng",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Lạnh",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "8. Ăn",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COAN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.AN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "AN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Thích nóng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Thích mát",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Ăn nhiều",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Ăn ít",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Đắng miệng",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Nhạt miệng",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Thèm ăn",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Chán ăn",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. Ăn vào bụng chướng",
                                                                    "value": "9"
                                                                },
                                                                {
                                                                    "label": "10. Khác",
                                                                    "value": "10"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "9. Uống",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COUONG",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Thích uống",
                                                    "validate": {
                                                        "custom": "if (data.UONG.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "UONG",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Mát",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Ấm nóng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Nhiều",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Ít",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "10. Đại, tiểu tiện",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "CODAITIEUTIEN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 2,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 2
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "10.1. Tiểu tiện",
                                                    "validate": {
                                                        "custom": "if (data.TIEUTIEN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TIEUTIEN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Vàng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Đỏ",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Đục",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Buốt",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Rắt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Không tự chủ",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Bí",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "10.2. Đại tiện",
                                                    "validate": {
                                                        "custom": "if (data.DAITIEN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "DAITIEN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Táo",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Nát",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Sống",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Lỏng",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Nhầy mũi",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Bí",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Khác",
                                                                    "value": "7"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        }
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "11. Ngủ",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "CONGU",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.NGU.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "NGU",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Khó vào giấc ngủ",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Hay tỉnh",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Hay mê",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Ngủ ít",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "12. Khả năng SD-SS",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COSDSS",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 2,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 2
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "12.1. Nam giới",
                                                    "validate": {
                                                        "custom": "if (data.BENHNAM.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "BENHNAM",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Liệt dương",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Di tinh",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Hoạt tinh",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Mộng tinh",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Lãnh tinh",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Khác",
                                                                    "value": "6"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "12.2. Nữ giới",
                                                    "validate": {
                                                        "custom": "if (data.BENHNU.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "BENHNU",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Vô sinh",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Động thai",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Sảy thai",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sảy thai liên tiếp",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        }
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "- Kinh nguyệt",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COKINHNGUYET",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 2,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 2
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "+ Rối loạn kinh nguyệt",
                                                    "validate": {
                                                        "custom": "if (data.RLKINHNGUYET.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "RLKINHNGUYET",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trước kỳ",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Sau kỳ",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Bế kinh",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "+ Thống kinh",
                                                    "validate": {
                                                        "custom": "if (data.THONGKINH.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "THONGKINH",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trước kỳ",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trong kỳ",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Sau kinh",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 5
                                        }
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "- Đới hạ",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "CODOIHA",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.DOIHA.length > 2) {\r\n    valid = \"Không thể chọn quá 2 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "DOIHA",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Vàng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trắng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Hồng",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Hôi",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "13. Các yếu tố liên quan đến xuất hiện bệnh",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COYEUTOLIENQUAN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "14. Mô tả khác (nếu có)",
                                                    "key": "MOTAKHACVANCHAN",
                                                    "type": "textarea",
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    "rows": 2,
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "IV. THIẾT CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "1. Xúc chẩn",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COXUCCHAN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "- Da",
                                                    "validate": {
                                                        "custom": "if (data.DA.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "DA",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Khô",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Nóng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Lạnh",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Ướt",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Chân tay nóng",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Chân tay lạnh",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Ấn lõm",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Ấn đau",
                                                                    "value": "8"
                                                                },
                                                                {
                                                                    "label": "9. U/Cục",
                                                                    "value": "9"
                                                                },
                                                                {
                                                                    "label": "10. Khác",
                                                                    "value": "10"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "- Cơ - xương - khớp",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COCOXUONGKHOP",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.COXUONGKHOP.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "COXUONGKHOP",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Mềm nhẽo",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Căn cứng",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Ấn đau",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Khác",
                                                                    "value": "4"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "- Bụng",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COBUNGTHIETCHAN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.BUNGTHIETCHAN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "BUNGTHIETCHAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Chướng",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Tích",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Tụ",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Thiện án",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Cự án",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Khác",
                                                                    "value": "6"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "- Mồ hôi",
                                                    others: {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Có",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Không",
                                                                    "value": "2"
                                                                },
                                                            ]
                                                        },
                                                    },
                                                    key: "COMOHOITHIETCHAN",
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 3,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 3
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "&nbsp;",
                                                    "validate": {
                                                        "custom": "if (data.MOHOITHIETCHAN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "MOHOITHIETCHAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Toàn thân",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trán",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Tay",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Chân",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Khác",
                                                                    "value": "5"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 9,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 9
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "2. Mạch chẩn",
                                                    "validate": {
                                                        "custom": "if (data.MACHCHAN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "MACHCHAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 10,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 10
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Lấy dữ liệu",
                                                    "customClass": "text-right form-control-sm line-height-1",
                                                    "key": "laydulieu",
                                                    "type": "button",
                                                    others: {
                                                        "leftIcon": "fa fa-ellipsis-v",
                                                        "action": "event",
                                                        "showValidations": false,
                                                        "event": "laydulieumachchan",
                                                        "type": "button",
                                                    }
                                                },
                                            ],
                                            "width": 2,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 2
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Tổng khán - Bên phải (Khí, dương)",
                                                    "validate": {
                                                        "custom": "if (data.TONGKHANPHAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TONGKHANPHAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Tổng khán - Bên trái (Huyết, âm)",
                                                    "validate": {
                                                        "custom": "if (data.TONGKHANTRAI.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TONGKHANTRAI",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay trái - Thốn",
                                                    "validate": {
                                                        "custom": "if (data.TAITRAITHON.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAITRAITHON",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay trái - Quan",
                                                    "validate": {
                                                        "custom": "if (data.TAITRAIQUAN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAITRAIQUAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay trái - Xích",
                                                    "validate": {
                                                        "custom": "if (data.TAITRAIXICH.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAITRAIXICH",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay phải - Thốn",
                                                    "validate": {
                                                        "custom": "if (data.TAIPHAITHON.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAIPHAITHON",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay phải - Quan",
                                                    "validate": {
                                                        "custom": "if (data.TAIPHAIQUAN.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAIPHAIQUAN",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Vi khán - Mạch tay phải - Xích",
                                                    "validate": {
                                                        "custom": "if (data.TAIPHAIXICH.length > 3) {\r\n    valid = \"Không thể chọn quá 3 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                                    },
                                                    "key": "TAIPHAIXICH",
                                                    others : {
                                                        "data": {
                                                            "values": [
                                                                {
                                                                    "label": "1. Trầm",
                                                                    "value": "1"
                                                                },
                                                                {
                                                                    "label": "2. Trì",
                                                                    "value": "2"
                                                                },
                                                                {
                                                                    "label": "3. Phù",
                                                                    "value": "3"
                                                                },
                                                                {
                                                                    "label": "4. Sác",
                                                                    "value": "4"
                                                                },
                                                                {
                                                                    "label": "5. Hoạt",
                                                                    "value": "5"
                                                                },
                                                                {
                                                                    "label": "6. Huyền",
                                                                    "value": "6"
                                                                },
                                                                {
                                                                    "label": "7. Nhu",
                                                                    "value": "7"
                                                                },
                                                                {
                                                                    "label": "8. Khác",
                                                                    "value": "8"
                                                                },
                                                            ]
                                                        },
                                                        "input": true,
                                                        "widget": "choicesjs",
                                                        "tableView": true,
                                                        "multiple": true,
                                                    },
                                                    "type": "select",
                                                },
                                            ],
                                            "width": 4,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "3. Mô tả khác (nếu có)",
                                    "key": "MOTATHIETCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    "rows": 2
                                },
                            ]
                        },
                    ]
                },
                {
                    "label": "V. TÓM TẮT TỨ CHẨN",
                    "key": "TOMTATTUCHAN",
                    "type": "textarea",
                    "validate": {
                        "maxLength": 3000,
                    },
                    "rows": 2,
                },
                {
                    "label": "VI. BIỆN CHỨNG LUẬN TRỊ",
                    "key": "BIENCHUNGLUANTRI",
                    "type": "textarea",
                    "validate": {
                        "maxLength": 3000,
                    },
                    "rows": 2,
                },

                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "VII. CHẨN ĐOÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            label: "",
                            key: "wrap_benhdanh",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "tag": "label",
                                            "attrs": [
                                                {
                                                    "attr": "",
                                                    "value": ""
                                                }
                                            ],
                                            "content": "1. Bệnh danh",
                                            "key": "htmllabel_benhdanh",
                                            "type": "htmlelement",
                                        },
                                    ],
                                    "width": 12,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "ICD_BENHDANH",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                            others: {
                                                "placeholder": "ICD",
                                            }
                                        },
                                    ],
                                    "width": 2,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "TENICD_BENHDANH",
                                            "type": "textfield",
                                            others: {
                                                "placeholder": "Tên bệnh",
                                            }
                                        },
                                    ],
                                    "width": 10,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "CHUANDOANBENHDANH",
                                            "type": "textarea",
                                            "rows": 2,
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                            "input": true,
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 10,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "",
                                            "key": "BENHDANH1",
                                            "type": "textfield",
                                        },
                                    ],
                                    "width": 2,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "2. Bát cương",
                                            "key": "CHUANDOANBATCUONG",
                                            "type": "textarea",
                                            "rows": 2,
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },
                                {
                                    "components": [
                                        {
                                            "label": "&nbsp;",
                                            "validate": {
                                                "custom": "if (data.BATCUONG.length > 5) {\r\n    valid = \"Không thể chọn quá 5 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                            },
                                            "key": "BATCUONG",
                                            others : {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Biểu",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Lý",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Hư",
                                                            "value": "3"
                                                        },
                                                        {
                                                            "label": "4. Thực",
                                                            "value": "4"
                                                        },
                                                        {
                                                            "label": "5. Hàn",
                                                            "value": "5"
                                                        },
                                                        {
                                                            "label": "6. Nhiệt",
                                                            "value": "6"
                                                        },
                                                        {
                                                            "label": "7. Âm",
                                                            "value": "7"
                                                        },
                                                        {
                                                            "label": "8. Dương",
                                                            "value": "8"
                                                        },
                                                    ]
                                                },
                                                "input": true,
                                                "widget": "choicesjs",
                                                "tableView": true,
                                                "multiple": true,
                                            },
                                            "type": "select",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "kb-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },
                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "3. Nguyên nhân",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Nội nhân",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Ngoại nhân",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Bất nội ngoại nhân",
                                                            "value": "3"
                                                        },
                                                    ]
                                                },
                                            },
                                            key: "NGUYENNHANCHANDOAN",
                                            "type": "select",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },
                                {
                                    "components": [
                                        {
                                            "label": "4. Tạng phủ",
                                            "validate": {
                                                "custom": "if (data.TANGPHU.length > 4) {\r\n    valid = \"Không thể chọn quá 4 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                            },
                                            "key": "TANGPHU",
                                            others : {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Tâm",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Can",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Tỳ",
                                                            "value": "3"
                                                        },
                                                        {
                                                            "label": "4. Phế",
                                                            "value": "4"
                                                        },
                                                        {
                                                            "label": "5. Thận",
                                                            "value": "5"
                                                        },
                                                        {
                                                            "label": "6. Tâm bào",
                                                            "value": "6"
                                                        },
                                                        {
                                                            "label": "7. Tiểu trường",
                                                            "value": "7"
                                                        },
                                                        {
                                                            "label": "8. Đờm",
                                                            "value": "8"
                                                        },
                                                        {
                                                            "label": "9. Vị",
                                                            "value": "9"
                                                        },
                                                        {
                                                            "label": "10. Đại trường",
                                                            "value": "10"
                                                        },
                                                        {
                                                            "label": "11. Bàng quang",
                                                            "value": "11"
                                                        },
                                                        {
                                                            "label": "12. Tam tiêu",
                                                            "value": "12"
                                                        },
                                                        {
                                                            "label": "13. Phủ kỳ hằng",
                                                            "value": "13"
                                                        },
                                                    ]
                                                },
                                                "input": true,
                                                "widget": "choicesjs",
                                                "tableView": true,
                                                "multiple": true,
                                            },
                                            "type": "select",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "kb-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },
                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "5. Kinh, mạch",
                                            "validate": {
                                                "custom": "if (data.KINHMACH.length > 4) {\r\n    valid = \"Không thể chọn quá 4 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                            },
                                            "key": "KINHMACH",
                                            others : {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Tâm",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Can",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Tỳ",
                                                            "value": "3"
                                                        },
                                                        {
                                                            "label": "4. Phế",
                                                            "value": "4"
                                                        },
                                                        {
                                                            "label": "5. Thận",
                                                            "value": "5"
                                                        },
                                                        {
                                                            "label": "6. Tâm bào",
                                                            "value": "6"
                                                        },
                                                        {
                                                            "label": "7. Tiểu trường",
                                                            "value": "7"
                                                        },
                                                        {
                                                            "label": "8. Đờm",
                                                            "value": "8"
                                                        },
                                                        {
                                                            "label": "9. Vị",
                                                            "value": "9"
                                                        },
                                                        {
                                                            "label": "10. Đại trường",
                                                            "value": "10"
                                                        },
                                                        {
                                                            "label": "11. Bàng quang",
                                                            "value": "11"
                                                        },
                                                        {
                                                            "label": "12. Tam tiêu",
                                                            "value": "12"
                                                        },
                                                        {
                                                            "label": "13. Mạch đốc",
                                                            "value": "13"
                                                        },
                                                        {
                                                            "label": "14. Mạch nhâm",
                                                            "value": "14"
                                                        },
                                                    ]
                                                },
                                                "input": true,
                                                "widget": "choicesjs",
                                                "tableView": true,
                                                "multiple": true,
                                            },
                                            "type": "select",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },
                                {
                                    "components": [
                                        {
                                            "label": "6. Định vị bệnh theo",
                                            "validate": {
                                                "custom": "if (data.DINHVIBENH.length > 4) {\r\n    valid = \"Không thể chọn quá 4 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                            },
                                            "key": "DINHVIBENH",
                                            others : {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Vệ",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Khí",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Dinh",
                                                            "value": "3"
                                                        },
                                                        {
                                                            "label": "4. Huyết",
                                                            "value": "4"
                                                        },
                                                    ]
                                                },
                                                "input": true,
                                                "widget": "choicesjs",
                                                "tableView": true,
                                                "multiple": true,
                                            },
                                            "type": "select",
                                        },
                                    ],
                                    "width": 6,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 6
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "kb-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "C. ĐIỀU TRỊ",
                    "title": "C. ĐIỀU TRỊ",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "I. Y HỌC CỔ TRUYỀN",
                            "title": "I. Y HỌC CỔ TRUYỀN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "1. Pháp điều trị",
                                    "key": "PHAPDIEUTRI",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "2.1. Phương dược",
                                    "key": "PHUONGDUOC",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "2.2. Phương pháp điều trị không dùng thuốc",
                                    "key": "PPKHONGDUNGTHUOC",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "2.3. Các phương pháp khác",
                                    "key": "PHUONGPHAPKHAC",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                            ]
                        }
                    ]
                },
                {
                    "label": "II. Y HỌC HIỆN ĐẠI",
                    others: {
                        'tooltip': 'Hướng điều trị: (Phương pháp điều trị, chế độ dinh dưỡng, chế độ chăm sóc,...)'
                    },
                    "key": "YHOCHIENDAI",
                    "type": "textarea",
                    "validate": {
                        "maxLength": 3000,
                    },
                    "rows": 2,
                },
                {
                    "label": "III. DỰ HẬU (TIÊN LƯỢNG)",
                    "key": "DUHAU",
                    "type": "textarea",
                    "validate": {
                        "maxLength": 3000,
                    },
                    "rows": 2,
                },
                getObjectThoigianBacsilambenhanFormio()
            ])
        },
        initObjectFormPage3: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-tongketdieutri",
                    "type": "panel",
                    "label": "TỔNG KẾT BỆNH ÁN",
                    "title": "TỔNG KẾT BỆNH ÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "Lý do vào viện",
                            "key": "LYDOVAOVIEN",
                            "type": "textarea",
                            customClass: "pr-2",
                            rows: 2,
                            "validate": {
                                "maxLength": 3000,
                                required: true
                            }
                        },
                        {
                            "label": "Copy diễn biến",
                            "customClass": "text-right form-control-sm line-height-1",
                            "key": "copydbbenhtdt",
                            "type": "button",
                            others: {
                                "leftIcon": "fa fa-ellipsis-v",
                                "action": "event",
                                "showValidations": false,
                                "event": "openmodalcopydbbenh",
                                "type": "button",
                            }

                        },
                        {
                            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
                            "key": "QUATRINH_BENHLY",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                                required: true
                            }
                        },
                        {
                        	"label": "Copy cận lâm sàng",
                        	"customClass": "text-right form-control-sm line-height-1",
                        	"key": "copytomtatcls",
                        	"type": "button",
                        	others: {
                        		"leftIcon": "fa fa-ellipsis-v",
                        		"action": "event",
                        		"showValidations": false,
                        		"event": "openmodalcopytomtatcls",
                        		"type": "button",
                        	}
                        },
                        {
                            "label": "Tóm tắt kết quả cận lâm sàng",
                            "key": "tomTatKetQuaXNCLS",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "collapsible": true,
                            "key": "p-tongketdieutri",
                            "type": "panel",
                            "label": "4. Chẩn đoán vào viện",
                            "title": "4. Chẩn đoán vào viện",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            components: [
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học hiện đại",
                                    "title": "   - Y học hiện đại",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_VAOVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_VAOVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDBENHKEMTHEO",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                },
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học cổ truyền",
                                    "title": "   - Y học cổ truyền",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_VAOVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_VAOVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTBENHKEMTHEO",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                }
                            ]
                        },
                        {
                            "label": "Phương pháp điều trị - Y học hiện đại",
                            "key": "YHHDPHUONGPHAPDIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                    required: true
                            }
                        },
                        {
                            "label": "Phương pháp điều trị - Y học cổ truyền",
                            "key": "YHCTPHUONGPHAPDIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                    required: true
                            }
                        },
                        {
                            "label": "6. Kết quả điều trị",
                            others: {
                                "data": {
                                    "values": [
                                        {
                                            "label": "1. Khỏi",
                                            "value": "1"
                                        },
                                        {
                                            "label": "2. Đỡ",
                                            "value": "2"
                                        },
                                        {
                                            "label": "3. Không thay đổi",
                                            "value": "3"
                                        },
                                        {
                                            "label": "4. Nặng hơn",
                                            "value": "4"
                                        },
                                        {
                                            "label": "5. Tử vong",
                                            "value": "5"
                                        },
                                    ]
                                },
                            },
                            validate: {
                                required: true
                            },
                            key: "KETQUADIEUTRI",
                            "type": "select",
                        },
                        {
                            "collapsible": true,
                            "key": "p-tongketdieutri",
                            "type": "panel",
                            "label": "7. Chẩn đoán ra viện",
                            "title": "7. Chẩn đoán ra viện",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            components: [
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học hiện đại",
                                    "title": "   - Y học hiện đại",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_RAVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_RAVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENBENHKEMTHEO_TEN",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                },
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học cổ truyền",
                                    "title": "   - Y học cổ truyền",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_RAVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_RAVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENBENHKEMTHEO_TEN",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                }
                            ]
                        },
                        {
                            "label": "8. Tình trạng người bệnh khi ra viện",
                            "key": "TINHTRANG_RAVIEN",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                                required: true
                            }
                        },
                        {
                            "label": "9. Hướng điều trị và các chế độ tiếp theo",
                            "key": "HUONG_DIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                                required: true
                            }
                        },
                    ]
                },
                getObjectThoigianTongketFormio()
            ]);
        },
        callbackAfterLoad: function (instance) {
            form = instance;
            var tenBenhchinhElement = form.getComponent('TENICD_BENHCHINH');
            var icdBenhchinhElement = form.getComponent('ICD_BENHCHINH');
            var tenBenhphuElement = form.getComponent('TENICD_BENHPHU');
            var icdBenhphuElement = form.getComponent('ICD_BENHPHU');
            var textBenhphuElement = form.getComponent('TENBENHPHU1');
            var tenBenhDanhElement = form.getComponent('TENICD_BENHDANH');
            var icdBenhDanhElement = form.getComponent('ICD_BENHDANH');
            var textBenhDanhElement = form.getComponent('CHUANDOANBENHDANH');
            var bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');
            var bmiElement = form.getComponent('BMI');
            var cannangElement = form.getComponent('CANNANG');
            var chieucaoElement = form.getComponent('CHIEUCAO');

            $("#"+getIdElmentFormio(form,'ICD_BENHCHINH')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'ICD_BENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'TENICD_BENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhphuElement.getValue();
                    var mabenhICD = icdBenhphuElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
                    }
                    icdBenhphuElement.setValue("")
                    tenBenhphuElement.setValue("")
                    icdBenhphuElement.focus()
                }
            })

            $("#"+getIdElmentFormio(form,'ICD_BENHDANH')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhDanhElement.setValue(splitIcd[1]);
                        tenBenhDanhElement.focus()
                        icdBenhDanhElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'TENICD_BENHDANH')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhDanhElement.getValue();
                    var mabenhICD = icdBenhDanhElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhDanhElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhDanhElement.getValue());
                    }
                    icdBenhDanhElement.setValue("")
                    tenBenhDanhElement.setValue("")
                    icdBenhDanhElement.focus()
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TENICD_BENHCHINH'), function(item) {
                icdBenhchinhElement.setValue(item.ICD);
                tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
            });
            combgridTenICD(getIdElmentFormio(form,'TENICD_BENHPHU'), function(item) {
                icdBenhphuElement.setValue(item.ICD);
                tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            });
            combgridTenICD(getIdElmentFormio(form,'TENICD_BENHDANH'), function(item) {
                icdBenhDanhElement.setValue(item.ICD);
                tenBenhDanhElement.setValue(item.MO_TA_BENH_LY);
            });

            $("#"+getIdElmentFormio(form,'MAKHOA')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
            })

            $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
                if(!$(this).val() || !chieucaoElement.getValue()) {
                    return;
                }
                bmiElement.setValue((chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            })

            $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
                if(!$(this).val() || !cannangElement.getValue()) {
                    return;
                }
                bmiElement.setValue((cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            });

            instance.on('openmodalcopycls', function(click) {
                addTextTitleModal("titleModalFormhsbacopyylenhcls")
                $("#modalFormhsbacopyylenhcls").modal("show");
                $(document).trigger("reloadDSCLSChiDinh");
            });

            instance.on('laydulieumachchan', function(click) {
                var machChanElement = form.getComponent('MACHCHAN');
                var tongKhanPhaiElement = form.getComponent('TONGKHANPHAI');
                var tongKhanTraiElement = form.getComponent('TONGKHANTRAI');
                var taiTraiThonElement = form.getComponent('TAITRAITHON');
                var taiTraiQuanElement = form.getComponent('TAITRAIQUAN');
                var taiTraiXichElement = form.getComponent('TAITRAIXICH');
                var taiPhaiThonElement = form.getComponent('TAIPHAITHON');
                var taiPhaiQuanElement = form.getComponent('TAIPHAIQUAN');
                var taiPhaiXichElement = form.getComponent('TAIPHAIXICH');

                tongKhanPhaiElement.setValue(machChanElement.getValue());
                tongKhanTraiElement.setValue(machChanElement.getValue());
                taiTraiThonElement.setValue(machChanElement.getValue());
                taiTraiQuanElement.setValue(machChanElement.getValue());
                taiTraiXichElement.setValue(machChanElement.getValue());
                taiPhaiThonElement.setValue(machChanElement.getValue());
                taiPhaiQuanElement.setValue(machChanElement.getValue());
                taiPhaiXichElement.setValue(machChanElement.getValue());
            });

            var idWrap = "hsba_vba_trang2-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'NOITRUYHCT', function(dataTrang2) {
                hideLoaderIntoWrapId(idWrap)
                delete dataTrang2.ID;
                dataTrang2.MAKHOA = dataTrang2.MAKHOA? dataTrang2.MAKHOA: singletonObject.makhoa;
                if(dataTrang2.BENHCHINH && dataTrang2.BENHCHINH.includes(" - ")) {
                    var splitIcd = dataTrang2.BENHCHINH.split(" - ");
                    dataTrang2.ICD_BENHCHINH = splitIcd[0];
                    dataTrang2.TENICD_BENHCHINH = splitIcd[1];
                }
                if(dataTrang2.BENHPHU && dataTrang2.BENHCHINH.includes(" - ")) {
                    let splitBenhPhu = dataTrang2.BENHPHU.split(";; ");
                    let benhPhu1 = splitBenhPhu[0];
                    let benhPhu2 = splitBenhPhu[1];

                    if (benhPhu1) {
                        let splitBenhPhu1 = benhPhu1.split(" - ");
                        dataTrang2.ICD_BENHPHU = splitBenhPhu1[0];
                        dataTrang2.TENICD_BENHPHU = splitBenhPhu1[1];
                    }
                    if (benhPhu2) {
                        dataTrang2.TENBENHPHU1 = benhPhu2;
                    }
                }
                dataTrang2.NGAYBSLAMBENHAN =  (dataTrang2.NGAYBSLAMBENHAN? moment(dataTrang2.NGAYBSLAMBENHAN): moment()).toISOString();
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TIENGNOI', 5)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TIENSU', 4)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HINHTHAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'THAN', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'SAC', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TRACH', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HINHLUOI', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'CHATLUOI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'REULUOI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HOITHO', 5)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HO', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'MUICOTHE', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'CHATTHAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HANNHIET', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'MOHOI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DAUDAU', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DOIMAT', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'MUI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'HONG', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'COVAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'NGUC', 6)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'BUNG', 6)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'CHANTAY', 6)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'AN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'UONG', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TIEUTIEN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DAITIEN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'NGU', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'BENHNAM', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'BENHNU', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'RLKINHNGUYET', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'THONGKINH', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DOIHA', 2)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DA', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'COXUONGKHOP', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'BUNGTHIETCHAN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'MOHOITHIETCHAN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'MACHCHAN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TONGKHANPHAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TONGKHANTRAI', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAITRAITHON', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAITRAIQUAN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAITRAIXICH', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAIPHAITHON', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAIPHAIQUAN', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TAIPHAIXICH', 3)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'BATCUONG', 5)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'TANGPHU', 4)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'KINHMACH', 4)
                mergeMultiSelectAddToObjectFormio(dataTrang2, 'DINHVIBENH', 4)

                getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);
                if (dataTrang2.MACH == null || dataTrang2.MACH == undefined || dataTrang2.MACH == "") {
                    var res = $.ajax({
                        url:"cmu_list_CMU_HSBA_GETDEF?url="+
                            convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]),
                        type:"GET",
                        async: false
                    }).responseText;

                    var dataDef = JSON.parse(res);
                    dataTrang2.MACH = dataDef[0].MACH;
                    dataTrang2.NHIETDO = dataDef[0].NHIETDO;
                    dataTrang2.NHIPTHO = dataDef[0].NHIPTHO;
                    dataTrang2.HUYETAPTREN = dataDef[0].HUYETAPTREN;
                    dataTrang2.HUYETAPDUOI = dataDef[0].HUYETAPDUOI;
                    dataTrang2.CANNANG = dataDef[0].CANNANG;
                    dataTrang2.CHIEUCAO = dataDef[0].CHIEUCAO;
                    if(!isNaN(dataTrang2.CANNANG) && !isNaN(dataTrang2.CHIEUCAO)) {
                        dataTrang2.BMI = (dataTrang2.CANNANG/Math.pow(dataTrang2.CHIEUCAO/100, 2)).toFixed(2);
                    }
                }
                form.submission =  {
                    data: {
                        ...dataTrang2
                    }
                };
                $("#"+getIdElmentFormio(form,'HINHTHAI')).change()
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        save: function(element) {
            var idButton = element.id;
            form.emit("checkValidity");
            if (!form.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = form.submission.data;
            delete dataSubmit.copyclstdt
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TIENGNOI', 5)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TIENSU', 4)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HINHTHAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'THAN', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'SAC', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TRACH', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HINHLUOI', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'CHATLUOI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'REULUOI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HOITHO', 5)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HO', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'MUICOTHE', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'CHATTHAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HANNHIET', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'MOHOI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DAUDAU', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DOIMAT', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'MUI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'HONG', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'COVAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'NGUC', 6)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'BUNG', 6)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'CHANTAY', 6)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'AN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'UONG', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TIEUTIEN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DAITIEN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'NGU', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'BENHNAM', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'BENHNU', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'RLKINHNGUYET', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'THONGKINH', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DOIHA', 2)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DA', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'COXUONGKHOP', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'BUNGTHIETCHAN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'MOHOITHIETCHAN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'MACHCHAN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TONGKHANPHAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TONGKHANTRAI', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAITRAITHON', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAITRAIQUAN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAITRAIXICH', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAIPHAITHON', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAIPHAIQUAN', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TAIPHAIXICH', 3)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'BATCUONG', 5)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TANGPHU', 4)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'KINHMACH', 4)
            splitMultiSelectAddToObjectFormio(dataSubmit, 'DINHVIBENH', 4)
            dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            var ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
            dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
            dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN')).split(" - ")[1];
            dataSubmit.BENHCHINH = dataSubmit.ICD_BENHCHINH + " - " + dataSubmit.TENICD_BENHCHINH;
            dataSubmit.BENHPHU = (dataSubmit.ICD_BENHPHU ? dataSubmit.ICD_BENHPHU : "") +
                                 (dataSubmit.ICD_BENHPHU && dataSubmit.TENICD_BENHPHU ? " - " : "") +
                                 (dataSubmit.TENICD_BENHPHU ? dataSubmit.TENICD_BENHPHU : "") +
                                 (dataSubmit.TENBENHPHU1 ? ";; " + dataSubmit.TENBENHPHU1 : "");
            console.log(dataSubmit)
            $.post("update-benhan-noitruyhct", dataSubmit)
                .done(function (data) {
                    notifiToClient("Green", "Lưu thành công")
                    updateNgaylamVaBSHSBA({
                        ...dataSubmit,
                        NGAYBA: ngayba.format("DD/MM/YYYY"),
                    })

                    var Logbandau = []
                    var Logmoi = []
                    var newdata = {};
                    console.log(oldDataTrang2)
                    dataSubmit.NGAYBSLAMBENHAN = moment(dataSubmit.NGAYBSLAMBENHAN).format("MM/DD/YYYY")
                    assignNonNullValuesBA(newdata,dataSubmit);
                    console.log(newdata)
                    var diffObject = findDifferencesBetweenObjects(oldDataTrang2, newdata);
                    console.log(diffObject)
                    for (let key in diffObject) {
                        if (keyLuuLogTrang2.hasOwnProperty(key)) {
                            Logbandau.push(getLabelValueBATrang2(key, oldDataTrang2))
                            Logmoi.push(getLabelValueBATrang2(key, newdata))
                        }
                    }
                    console.log(Logbandau)
                    console.log(Logmoi)
                    if (Logbandau.length != 0 || Logmoi.length != 0){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                            NOIDUNGBANDAU: Logbandau.join(";"),
                            NOIDUNGMOI: Logmoi.join(";"),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                    }

                }).fail(function () {
                notifiToClient("Red", "Lỗi lưu thông tin")
            }).always(function () {
                hideSelfLoading(idButton);
            })
        },
        callbackAfterLoadTongket: function (instance) {
            formTongket = instance;
            var bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');
            var idWrap = "hsba_vba_trang3-tab";
            $("#"+getIdElmentFormio(formTongket,'MAKHOA_KETHUC')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
            })

            $("#"+getIdElmentFormio(formTongket,'YHHDICDBENHCHINH')).on('keypress', function(event) {
                var tenBenhchinhElement = formTongket.getComponent('YHHDICDBENHCHINH_TEN');
                var icdBenhchinhElement = formTongket.getComponent('YHHDICDBENHCHINH');
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD);
                    })
                }
            });

            var icdBenhPhuVaoVienYHHDElement = formTongket.getComponent('ICD_VAOVIEN_YHHDBENHPHU');
            var tenBenhPhuVaoVienYHHDElement = formTongket.getComponent('TENICD_VAOVIEN_YHHDBENHPHU');
            var textBenhPhuVaoVienYHHDElement = formTongket.getComponent('YHHDBENHKEMTHEO');
            $("#"+getIdElmentFormio(formTongket,'ICD_VAOVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhPhuVaoVienYHHDElement.setValue(splitIcd[1]);
                        tenBenhPhuVaoVienYHHDElement.focus()
                        icdBenhPhuVaoVienYHHDElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(formTongket,'TENICD_VAOVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhPhuVaoVienYHHDElement.getValue();
                    var mabenhICD = icdBenhPhuVaoVienYHHDElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuVaoVienYHHDElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuVaoVienYHHDElement.getValue());
                    }
                    icdBenhPhuVaoVienYHHDElement.setValue("")
                    tenBenhPhuVaoVienYHHDElement.setValue("")
                    icdBenhPhuVaoVienYHHDElement.focus()
                }
            })

            $("#"+getIdElmentFormio(formTongket,'YHCTICDBENHCHINH')).on('keypress', function(event) {
                var tenBenhchinhElement = formTongket.getComponent('YHCTICDBENHCHINH_TEN');
                var icdBenhchinhElement = formTongket.getComponent('YHCTICDBENHCHINH');
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });

            var icdBenhPhuVaoVienYHCTElement = formTongket.getComponent('ICD_VAOVIEN_YHCTBENHPHU');
            var tenBenhPhuVaoVienYHCTElement = formTongket.getComponent('TENICD_VAOVIEN_YHCTBENHPHU');
            var textBenhPhuVaoVienYHCTElement = formTongket.getComponent('YHCTBENHKEMTHEO');
            $("#"+getIdElmentFormio(formTongket,'ICD_VAOVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhPhuVaoVienYHCTElement.setValue(splitIcd[1]);
                        tenBenhPhuVaoVienYHCTElement.focus()
                        icdBenhPhuVaoVienYHCTElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_VAOVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhPhuVaoVienYHCTElement.getValue();
                    var mabenhICD = icdBenhPhuVaoVienYHCTElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuVaoVienYHCTElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuVaoVienYHCTElement.getValue());
                    }
                    icdBenhPhuVaoVienYHCTElement.setValue("")
                    tenBenhPhuVaoVienYHCTElement.setValue("")
                    icdBenhPhuVaoVienYHCTElement.focus()
                }
            });

            $("#"+getIdElmentFormio(formTongket,'YHHDRAVIENICDBENHCHINH')).on('keypress', function(event) {
                var tenBenhchinhElement = formTongket.getComponent('YHHDRAVIENICDBENHCHINH_TEN');
                var icdBenhchinhElement = formTongket.getComponent('YHHDRAVIENICDBENHCHINH');
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });

            var icdBenhPhuRaVienYHHDElement = formTongket.getComponent('ICD_RAVIEN_YHHDBENHPHU');
            var tenBenhPhuRaVienYHHDElement = formTongket.getComponent('TENICD_RAVIEN_YHHDBENHPHU');
            var textBenhPhuRaVienYHHDElement = formTongket.getComponent('YHHDRAVIENBENHKEMTHEO_TEN');
            $("#"+getIdElmentFormio(formTongket,'ICD_RAVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhPhuRaVienYHHDElement.setValue(splitIcd[1]);
                        tenBenhPhuRaVienYHHDElement.focus()
                        icdBenhPhuRaVienYHHDElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_RAVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhPhuRaVienYHHDElement.getValue();
                    var mabenhICD = icdBenhPhuRaVienYHHDElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuRaVienYHHDElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuRaVienYHHDElement.getValue());
                    }
                    icdBenhPhuRaVienYHHDElement.setValue("")
                    tenBenhPhuRaVienYHHDElement.setValue("")
                    icdBenhPhuRaVienYHHDElement.focus()
                }
            });

            $("#"+getIdElmentFormio(formTongket,'YHCTRAVIENICDBENHCHINH')).on('keypress', function(event) {
                var tenBenhchinhElement = formTongket.getComponent('YHCTRAVIENICDBENHCHINH_TEN');
                var icdBenhchinhElement = formTongket.getComponent('YHCTRAVIENICDBENHCHINH');
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });
            var icdBenhPhuRaVienYHCTElement = formTongket.getComponent('ICD_RAVIEN_YHCTBENHPHU');
            var tenBenhPhuRaVienYHCTElement = formTongket.getComponent('TENICD_RAVIEN_YHCTBENHPHU');
            var textBenhPhuRaVienYHCTElement = formTongket.getComponent('YHCTRAVIENBENHKEMTHEO_TEN');
            $("#"+getIdElmentFormio(formTongket,'ICD_RAVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhPhuRaVienYHCTElement.setValue(splitIcd[1]);
                        tenBenhPhuRaVienYHCTElement.focus()
                        icdBenhPhuRaVienYHCTElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_RAVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhPhuRaVienYHCTElement.getValue();
                    var mabenhICD = icdBenhPhuRaVienYHCTElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuRaVienYHCTElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuRaVienYHCTElement.getValue());
                    }
                    icdBenhPhuRaVienYHCTElement.setValue("")
                    tenBenhPhuRaVienYHCTElement.setValue("")
                    icdBenhPhuRaVienYHCTElement.focus()
                }
            });

            instance.on('openmodalcopytomtatcls', function(click) {
                addTextTitleModal("titleModalTomtatketCLSDieutri")
                $("#modalTomtatketCLSDieutri").modal("show");
                $(document).trigger("reloadDSTomtatCLS");
                $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
            });

            instance.on('openmodalcopytomtatcls', function(click) {
                var url = "cmu_getlist?url=" + convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    "CMU_DIENBIENBENH_SEL"
                ]);

                $.get(url).done(function(data) {
                    if (data && data.length > 0) {
                        var list = data.map(function(item) {
                            return `${item.DIENBIEN} (Tờ điều trị số: ${item.STT_DIEUTRI})`;
                        }).join('\n');

                        console.log("Danh sách diễn biến bệnh:" + list);

                    } else {
                        notifiToClient("Red", "Không có dữ liệu chế phẩm máu");
                    }
                }).fail(function() {
                    notifiToClient("Red", "Lỗi khi tải dữ liệu chế phẩm máu");
                });
            });
            showLoaderIntoWrapId(idWrap)
            getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'NOITRUYHCT', function(dataTrang3) {
                hideLoaderIntoWrapId(idWrap)
                formTongket.getComponent('MANHANVIEN_GIAOHOSO', function(component) {
                    danhsachnhanvien = component.component.data.values;
                });
                var NGUOIGIAO_HOSO = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.NGUOIGIAO_HOSO));
                var NGUOINHAN_HOSO = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.NGUOINHAN_HOSO));
                var BACSIDIEUTRI = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.BACSIDIEUTRI));
                dataTrang3.KETQUADIEUTRI = dataTrang3.KETQUADIEUTRI ? dataTrang3.KETQUADIEUTRI.trim() : "";
                dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;
                dataTrang3.soToCTScanner = dataTrang3.SOTO_CTSCANNER ? dataTrang3.SOTO_CTSCANNER : 0;
                dataTrang3.soToKhac = dataTrang3.SOTO_KHAC ? dataTrang3.SOTO_KHAC : 0;
                dataTrang3.soToMri = dataTrang3.SOTO_MRI ? dataTrang3.SOTO_MRI : 0;
                dataTrang3.soToSieuAm = dataTrang3.SOTO_SIEUAM ? dataTrang3.SOTO_SIEUAM : 0;
                dataTrang3.soToXQuang = dataTrang3.SOTO_XQUANG ? dataTrang3.SOTO_XQUANG : 0;
                dataTrang3.soToXetNghiem = dataTrang3.SOTO_XETNGHIEM ? dataTrang3.SOTO_XETNGHIEM : 0;
                dataTrang3.toanBoHoSo = dataTrang3.SOTO_TOANBOHS ? dataTrang3.SOTO_TOANBOHS : 0;
                dataTrang3.NGAY_TONGKET = (dataTrang3.NGAY_TONGKET_DATETIME? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']): moment()).toISOString();
                dataTrang3.MABACSIDIEUTRI = BACSIDIEUTRI ? BACSIDIEUTRI.value: "";
                dataTrang3.MANHANVIEN_GIAOHOSO = NGUOIGIAO_HOSO ? NGUOIGIAO_HOSO.value: "";
                dataTrang3.MANHANVIEN_NHANHOSO = NGUOINHAN_HOSO ? NGUOINHAN_HOSO.value: "";
                dataTrang3.tomTatKetQuaXNCLS = dataTrang3.TOMTAT_KETQUA;
                formTongket.submission = {
                    data: {
                        ...dataTrang3
                    }
                };
                getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        saveTongket: function(element, callBackSave) {
            var idButton = element.id;
            formTongket.emit("checkValidity");
            if (!formTongket.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = formTongket.submission.data;
            dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            dataSubmit.bacSiDieuTri =  getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
            dataSubmit.benh = "";
            dataSubmit.chiTietThuThuatPhauThuat = "";
            dataSubmit.giaiPhauBenh = "";
            dataSubmit.huongDieuTriVaCacCheDo = dataSubmit.HUONG_DIEUTRI;
            dataSubmit.phuongPhapDieuTri = dataSubmit.YHCTPHUONGPHAPDIEUTRI;
            dataSubmit.ketQuaDieuTri = dataSubmit.KETQUADIEUTRI;
            dataSubmit.lyDoVaoVien = dataSubmit.LYDOVAOVIEN;
            var ngayba = moment(dataSubmit.NGAY_TONGKET)
            dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
            dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
            dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO'));
            dataSubmit.quaTrinhBenhLy = dataSubmit.QUATRINH_BENHLY;
            dataSubmit.thuThuatPhauThuat = "";
            dataSubmit.tinhTrangNguoiBenhRaVien = dataSubmit.TINHTRANG_RAVIEN;

            dataSubmit.yHCTBenhKemTheo = dataSubmit.YHCTBENHKEMTHEO;
            dataSubmit.yHCTICDBenhChinh = dataSubmit.YHCTICDBENHCHINH;
            dataSubmit.yHCTICDBenhChinh_Ten = dataSubmit.YHCTICDBENHCHINH_TEN;

            dataSubmit.yHCTRaVienBenhKemTheo = dataSubmit.YHCTRAVIENBENHKEMTHEO_TEN;
            dataSubmit.yHCTRaVienICDBenhChinh = dataSubmit.YHCTRAVIENICDBENHCHINH;
            dataSubmit.yHCTRaVienICDBenhChinh_Ten = dataSubmit.YHCTRAVIENICDBENHCHINH_TEN;
            dataSubmit.yHCTphuongPhapDieuTri = dataSubmit.YHCTPHUONGPHAPDIEUTRI;

            dataSubmit.yHHDBenhKemTheo = dataSubmit.YHHDBENHKEMTHEO;
            dataSubmit.yHHDICDBenhChinh = dataSubmit.YHHDICDBENHCHINH;
            dataSubmit.yHHDICDBenhChinh_Ten = dataSubmit.YHHDICDBENHCHINH_TEN;

            dataSubmit.yHHDRaVienBenhKemTheo = dataSubmit.YHHDRAVIENBENHKEMTHEO_TEN;
            dataSubmit.yHHDRaVienICDBenhChinh = dataSubmit.YHHDRAVIENICDBENHCHINH;
            dataSubmit.yHHDRaVienICDBenhChinh_Ten = dataSubmit.YHHDRAVIENICDBENHCHINH_TEN;
            dataSubmit.yHHDphuongPhapDieuTri = dataSubmit.YHHDPHUONGPHAPDIEUTRI;


            $.ajax({
                url: "TongKetBenhAn_UpdateYHCTNoiTru",
                type: 'POST',
                data: JSON.stringify(dataSubmit),
                contentType: 'application/json',
                success: function (data) {
                    if (data.SUCCESS == 1) {
                        !callBackSave && notifiToClient("Green", "Lưu thành công")
                        updateThongtinPage3(dataSubmit, function () {
                            callBackSave && callBackSave({keyword: "Bác sĩ điều trị"});
                        });
                        !callBackSave && hideSelfLoading(idButton);

                        var Logbandau = []
                        var Logmoi = []
                        var newdata = {};
                        var dataSubmitnew = formTongket.submission.data;

                        dataSubmitnew.BACSIDIEUTRI = dataSubmitnew.bacSiDieuTri;
                        dataSubmitnew.ID = dataSubmitnew.id;
                        dataSubmitnew.LOAI_GIAYTO_KHAC = dataSubmitnew.loaiGiayToKhac;
                        var ngaytknew = moment(dataSubmitnew.NGAY_TONGKET)
                        dataSubmitnew.NGAY_TONGKET = ngaytknew.format("HH") + " Giờ " + ngaytknew.format("mm") + " phút, " + "Ngày " + ngaytknew.format("DD") + " tháng " + ngaytknew.format("MM") + " năm " + ngaytknew.format("YYYY");
                        dataSubmitnew.NGUOIGIAO_HOSO = dataSubmitnew.nguoiGiaoHoSo;
                        dataSubmitnew.NGUOINHAN_HOSO = dataSubmitnew.nguoiNhanHoSo;
                        dataSubmitnew.PHUONGPHAP_DIEUTRI = dataSubmitnew.phuongPhapDieuTri;
                        dataSubmitnew.QUATRINH_BENHLY = dataSubmitnew.quaTrinhBenhLy;
                        dataSubmitnew.SOTO_CTSCANNER = dataSubmitnew.soToCTScanner;
                        dataSubmitnew.SOTO_KHAC = dataSubmitnew.soToKhac;
                        dataSubmitnew.SOTO_SIEUAM = dataSubmitnew.soToSieuAm;
                        dataSubmitnew.SOTO_XQUANG = dataSubmitnew.soToXQuang;
                        dataSubmitnew.SOTO_XETNGHIEM = dataSubmitnew.soToXetNghiem;
                        dataSubmitnew.THUTHUAT_PHAUTHUAT = dataSubmitnew.thuThuatPhauThuat;
                        dataSubmitnew.TINHTRANG_RAVIEN = dataSubmitnew.tinhTrangNguoiBenhRaVien;
                        dataSubmitnew.SOTO_TOANBOHS = dataSubmitnew.toanBoHoSo;

                        assignNonNullValuesBA(newdata,dataSubmitnew);
                        var diffObject = findDifferencesBetweenObjects(oldDataTrang3, newdata);
                        console.log(diffObject)
                        for (let key in diffObject) {
                            if (keyLuuLogTrang3.hasOwnProperty(key)) {
                                Logbandau.push(getLabelValueBATrang3(key, oldDataTrang3))
                                Logmoi.push(getLabelValueBATrang3(key, newdata))
                            }
                        }
                        console.log(Logbandau)
                        console.log(Logmoi)
                        if (Logbandau.length != 0 || Logmoi.length != 0){
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                                NOIDUNGBANDAU: Logbandau.join(";"),
                                NOIDUNGMOI: Logmoi.join(";"),
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.EDIT.KEY,
                            })
                        }

                    } else {
                        notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
                    }
                    hideSelfLoading(idButton);
                },
                error: function (error) {
                    notifiToClient("Red", "Lỗi lưu thông tin")
                    hideSelfLoading(idButton);
                }
            })

        },
        callbackAfterLoadPage1: function (instance) {
            formPage1 = instance;
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap);
            var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO;
            const promises = [
                actionLoadObjectQuanLyNguoiBenhVBAT1_1(formPage1, dataTrang1),
                actionLoadObjectChanDoanVBAT1_3(formPage1, dataTrang1),
                actionLoadObjectTinhTrangRaVienVBAT1_1(formPage1, dataTrang1)
            ];
            Promise.all(promises)
                .then(results => {
                    formPage1.submission =  {
                        data: {
                            ...dataTrang1
                        }
                    };
                    hideLoaderIntoWrapId(idWrap)
                })
                .catch(error => {
                    console.error("An error occurred:", error);
                    hideLoaderIntoWrapId(idWrap);
                });
        },
        savePage1: function(element) {
            var idButton = element.id;
            formPage1.emit("checkValidity");
            if (!formPage1.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = formPage1.submission.data;
            var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
            dataTrang1.INFO.CHUYENKHOASONGAY0 = dataSubmit.CHUYENKHOASONGAY0;
            dataTrang1.INFO.CHUYENKHOATHOIGIAN0 = moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss");
            dataTrang1.INFO.ICD_KHOADT = dataSubmit.ICD_KHOADT;
            dataTrang1.INFO.ICD_KHOADT_TEN = dataSubmit.ICD_KHOADT_TEN;

            dataTrang1.INFO.NOIGIOITHIEU = dataSubmit.NOIGIOITHIEU;
            dataTrang1.INFO.VAOVIENLANTHU = dataSubmit.VAOVIENLANTHU;
            // Thêm CĐ YHHĐ
            dataTrang1.INFO.THUTHUAT_PHAUTHUAT = dataSubmit.THUTHUAT_PHAUTHUAT;
            dataTrang1.INFO.ICDPHU1_KHOADT = dataSubmit.ICDPHU1_KHOADT;
            dataTrang1.INFO.ICDPHU1_KHOADT_TEN = dataSubmit.ICDPHU1_KHOADT_TEN;
            dataTrang1.INFO.TAIBIEN_BIENCHUNG = dataSubmit.TAIBIEN_BIENCHUNG;

            // Thêm CĐ YHCT
            dataTrang1.INFO.ICDYHCT_KHOADT = dataSubmit.ICDYHCT_KHOADT;
            dataTrang1.INFO.ICDYHCT_KHOADT_TEN = dataSubmit.ICDYHCT_KHOADT_TEN;
            dataTrang1.INFO.ICDYHCTPHU1_KHOADT = dataSubmit.ICDYHCTPHU1_KHOADT;
            dataTrang1.INFO.ICDYHCTPHU1_KHOADT_TEN = dataSubmit.ICDYHCTPHU1_KHOADT_TEN;
            dataTrang1.INFO.YHCT_THUTHUAT_PHAUTHUAT = dataSubmit.YHCT_THUTHUAT_PHAUTHUAT;
            dataTrang1.INFO.YHCT_TAIBIEN_BIENCHUNG = dataSubmit.YHCT_TAIBIEN_BIENCHUNG;
            // Ra viện
            dataTrang1.INFO.GIAIPHAUBENH = dataSubmit.GIAIPHAUBENH;
            dataTrang1.INFO.NN_TUVONG = dataSubmit.NN_TUVONG;
            dataTrang1.INFO.KHOANGTG_TUVONG = dataSubmit.KHOANGTG_TUVONG;
            dataTrang1.INFO.KHAMNGHIEM = dataSubmit.KHAMNGHIEM == true ? 1 : 0;
            dataTrang1.INFO.ICD_GIAIPHAU = dataSubmit.ICD_GIAIPHAU;
            dataTrang1.INFO.TEN_ICD_GIAIPHAU = "";
            luuThongTinVBATrang1();

            var Logbandau = []
            var Logmoi = []
            var newdata = {};
            assignNonNullValuesTrang1(newdata,dataSubmit);
            var diffObject = findDifferencesBetweenObjects(oldDataTrang1, newdata);
            console.log(diffObject)
            for (let key in diffObject) {
                if (keyLuuLogTrang1.hasOwnProperty(key)) {
                    Logbandau.push(getLabelValueBATrang1(key, oldDataTrang1))
                    Logmoi.push(getLabelValueBATrang1(key, newdata))
                }
            }
            console.log(Logbandau)
            console.log(Logmoi)
            if (Logbandau.length != 0 || Logmoi.length != 0){
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                    NOIDUNGBANDAU: Logbandau.join(";"),
                    NOIDUNGMOI: Logmoi.join(";"),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                })
            }

            reloadFormVBAPage1(0, 0, idButton);
        },
        saveThongtinHC: function(element) {
            var idButton = element.id;
            showSelfLoading(idButton);
            var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
            updateQuanlynbvaChandoan(dataSubmit, function() {
                hideSelfLoading(idButton);
                notifiToClient("Green", "Lưu thành công")
            }, function() {
                hideSelfLoading(idButton);
                notifiToClient("Red", "Lỗi lưu thông tin")
            });
        },
        loadThongtinPage1: function() {
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function(response) {

                hideLoaderIntoWrapId(idWrap)
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin")
            });
        },
        copyChidinhCLS: function(cls) {
            form.submission = {
                data: {
                    ...form.submission.data,
                    CLS: cls
                }
            }
        },
        getInfoMauHSBA: function() {
            this.extendFunctionMau();
            return {
                keyMauHSBA: keyMauHSBANOITRUYHCT,
                insertMau: "insertMauHSBANOITRUYHCT",
                editMau: "editMauHSBANOITRUYHCT",
                selectMau: "selectMauHSBANOITRUYHCT",
                getdataMau: "getdataMauHSBANOITRUYHCT",
                formioValidate: "formioHSBANOITRUYHCTValidate",
            };
        },
        extendFunctionMau: function() {
            var self = this
            $.extend({
                insertMauHSBANOITRUYHCT: function () {
                    self.generateFormMauHSBA({})
                },
                editMauHSBANOITRUYHCT: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBA({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANOITRUYHCT: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...form.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    form.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANOITRUYHCT: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBA().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBA.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBA.submission.data.ID,
                        TENMAU: formioMauHSBA.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANOITRUYHCT
                    };
                },
                formioHSBANOITRUYHCTValidate: function() {
                    formioMauHSBA.emit("checkValidity");
                    if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBA: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBA = form;
                formioMauHSBA.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBA: function() {
            return getObjectMauHSBANOITRUYHCTPAGE2();
        },

        getInfoMauHSBATongket: function() {
            this.extendFunctionMauTongket();
            return {
                keyMauHSBA: keyMauHSBANOITRUYHCTTongket,
                insertMau: "insertMauHSBANOITRUYHCTTongket",
                editMau: "editMauHSBANOITRUYHCTTongket",
                selectMau: "selectMauHSBANOITRUYHCTTongket",
                getdataMau: "getdataMauHSBANOITRUYHCTTongket",
                formioValidate: "formioHSBANOITRUYHCTTongketValidate",
            };
        },
        extendFunctionMauTongket: function() {
            var self = this
            $.extend({
                insertMauHSBANOITRUYHCTTongket: function () {
                    self.generateFormMauHSBATongket({})
                },
                editMauHSBANOITRUYHCTTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBATongket({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANOITRUYHCTTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...formTongket.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    formTongket.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANOITRUYHCTTongket: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBATongket().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBATongket.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBATongket.submission.data.ID,
                        TENMAU: formioMauHSBATongket.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANOITRUYHCTTongket
                    };
                },
                formioHSBANOITRUYHCTTongketValidate: function() {
                    formioMauHSBATongket.emit("checkValidity");
                    if (!formioMauHSBATongket.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBATongket: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBATongket = form;
                formioMauHSBATongket.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBATongket: function() {
            return getObjectMauTongketYHCT();
        }
    }
}
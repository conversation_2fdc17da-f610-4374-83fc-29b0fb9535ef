create or replace FUNCTION             HSBA_TODIEUTRI_CMU_SEL(
    p_stt_benhan     IN VARCHAR2,
    p_makhoa IN VARCHAR2,
    p_userid IN VARCHAR2,
    p_dvtt           IN VARCHAR2
) return SYS_REFCURSOR
IS
  V_THAMSO_94300           VARCHAR2(1) := cmu_tsdv(p_dvtt, 94300, 0);
  V_THAMSO_86979           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86979, 0);
  v_kiemtrathuoc           number(11) default 0;
  V_THAMSO_86971           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86971, 0);
  v_thamso_bo_phieuhoantra number(1) default '0';
  v_svv                    number(11);
  v_svv_dt                 number(11);
  v_disable_auto           number(10) := cmu_tsdv(p_dvtt, 96206, 0);
  v_disable_auto_ttpt      number(10) := cmu_tsdv(p_dvtt, 96212, 0);
	cur SYS_REFCURSOR;
	v_sovaovien number;
	v_stt_dotdieutri number;
	v_phongbenh varchar2(255);
	v_sogiuong varchar2(255);
    v_thamso960615 varchar2(5):= HIS_MANAGER.DM_TSDV_SL_MTSO(p_dvtt, '960615');
BEGIN


select SOVAOVIEN into v_sovaovien from NOITRU_BENHAN
where stt_benhan = p_stt_benhan and dvtt = p_dvtt;
delete from NOITRU_DIEUTRI_HSBA_TEMP
where dvtt = p_Dvtt and STT_BENHAN = p_stt_benhan;

DELETE FROM tem_noitru_todieutri_in
WHERE dvtt = p_dvtt
  and stt_benhan = p_stt_benhan;


delete from NOITRU_CT_TOA_THUOC_TEMP
where dvtt = P_dvtt
  and STT_BENHAN =p_stt_benhan;

insert into NOITRU_DIEUTRI_HSBA_TEMP
(
    DVTT,
    STT_DIEUTRI,
    STT_DOTDIEUTRI,
    MABENHNHAN,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    ID_DIEUTRI,
    STT_BENHAN,
    NGAYGIOLAP_TDT,
    TDT_NGUOILAP,
    TDT_DIENBIENBENH,
    TDT_YLENH,
    ICD_DIEUTRI,
    TENICD_DIEUTRI,
    TEN_BENHPHU,
    BMI,
    MACH,
    NHIETDO,
    HUYETAPTREN,
    HUYETAPDUOI,
    CHIEUCAO,
    NHIPTHO,
    CANNANG,
    NGUOITAO,
    SPO2
)
select
    DVTT,
    STT_DIEUTRI,
    STT_DOTDIEUTRI,
    MABENHNHAN,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    ID_DIEUTRI,
    STT_BENHAN,
    NGAYGIOLAP_TDT,
    TDT_NGUOILAP,
    TDT_DIENBIENBENH,
    TDT_YLENH,
    ICD_DIEUTRI,
    TENICD_DIEUTRI,
    TEN_BENHPHU,
    BMI,
    MACH,
    NHIETDO,
    HUYETAPTREN,
    HUYETAPDUOI,
    CHIEUCAO,
    NHIPTHO,
    CANNANG,
    NGUOITAO,
    SPO2
from NOITRU_DIEUTRI
where dvtt = p_Dvtt and STT_BENHAN = p_stt_benhan and sovaovien = v_sovaovien
  and (p_makhoa = 0 or KHOALAP = p_makhoa) and (p_userid = -1 or TDT_NGUOILAP = p_userid);


insert into NOITRU_CT_TOA_THUOC_TEMP
(STT_TOATHUOC,
 STT_ORDER,
 DVTT,
 MA_TOA_THUOC,
 MAKHOVATTU,
 MAVATTU,
 TEN_VAT_TU,
 HOAT_CHAT,
 DVT,
 NGHIEP_VU,
 DONGIA_BAN_BV,
 DONGIA_BAN_BH,
 THANHTIEN_THUOC,
 SO_NGAY_UONG,
 SANG_UONG,
 TRUA_UONG,
 CHIEU_UONG,
 TOI_UONG,
 XAC_NHAN,
 NGAY_RA_TOA,
 GHI_CHU_CT_TOA_THUOC,
 MA_BAC_SI_THEMTHUOC,
 DATHANHTOAN,
 CACH_SU_DUNG,
 NAM,
 STT_DIEUTRI,
 STT_BENHAN,
 STT_DOTDIEUTRI,
 TU_TU_THUOC,
 NGAY,
 THANG,
 MABENHNHAN,
 KHOALAP,
 TRANG_THAI,
 KEY_QL,
 SOLOSANXUAT,
 NGAYSANXUAT,
 MALOAIVATTU,
 NHAPBANDAU,
 TT_THANHTOAN,
 NGAY_TATTOAN,
 KYHIEUNHOMBC,
 HAM_LUONG,
 DUONG_DUNG,
 SO_DANG_KY,
 SOVAOVIEN,
 SOVAOVIEN_DT,
 SONHAPKHOCHITIET,
 ID_DIEUTRI,
 BANT,
 NGOAI_DANH_MUC,
 SO_LUONG,
 SO_LUONG_THUC_LINH,
 SO_TIEN_DA_TT_CK,
 MA_DICH_VU_VTYT,
 THUOC_GOIVATTUKTC,
 MA_DV_CHIDINH_VTYT,
 VATTU_CHUNGGOIVTYT,
 VATTU_STTGOIVTYT,
 NGAYGIO)
select
    STT_TOATHUOC,
    STT_ORDER,
    DVTT,
    MA_TOA_THUOC,
    MAKHOVATTU,
    MAVATTU,
    TEN_VAT_TU,
    HOAT_CHAT,
    DVT,
    NGHIEP_VU,
    DONGIA_BAN_BV,
    DONGIA_BAN_BH,
    THANHTIEN_THUOC,
    SO_NGAY_UONG,
    SANG_UONG,
    TRUA_UONG,
    CHIEU_UONG,
    TOI_UONG,
    XAC_NHAN,
    NGAY_RA_TOA,
    GHI_CHU_CT_TOA_THUOC,
    MA_BAC_SI_THEMTHUOC,
    DATHANHTOAN,
    CACH_SU_DUNG,
    NAM,
    STT_DIEUTRI,
    STT_BENHAN,
    STT_DOTDIEUTRI,
    TU_TU_THUOC,
    NGAY,
    THANG,
    MABENHNHAN,
    KHOALAP,
    TRANG_THAI,
    KEY_QL,
    SOLOSANXUAT,
    NGAYSANXUAT,
    MALOAIVATTU,
    NHAPBANDAU,
    TT_THANHTOAN,
    NGAY_TATTOAN,
    'THUOC' KYHIEUNHOMBC,
    HAM_LUONG,
    DUONG_DUNG,
    SO_DANG_KY,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    SONHAPKHOCHITIET,
    ID_DIEUTRI,
    BANT,
    NGOAI_DANH_MUC,
    SO_LUONG,
    SO_LUONG_THUC_LINH,
    SO_TIEN_DA_TT_CK,
    MA_DICH_VU_VTYT,
    THUOC_GOIVATTUKTC,
    MA_DV_CHIDINH_VTYT,
    VATTU_CHUNGGOIVTYT,
    VATTU_STTGOIVTYT,
    NGAYGIO
from (
         select toathuoc.STT_TOATHUOC,
                STT_ORDER,
                dt.DVTT,
                MA_TOA_THUOC,
                MAKHOVATTU,
                toathuoc.MAVATTU,
                TEN_VAT_TU,
                HOAT_CHAT,
                DVT,
                NGHIEP_VU,
                DONGIA_BAN_BV,
                DONGIA_BAN_BH,
                THANHTIEN_THUOC,
                SO_NGAY_UONG,
                SANG_UONG,
                TRUA_UONG,
                CHIEU_UONG,
                TOI_UONG,
                XAC_NHAN,
                NGAY_RA_TOA,
                GHI_CHU_CT_TOA_THUOC,
                MA_BAC_SI_THEMTHUOC,
                DATHANHTOAN,
                CACH_SU_DUNG,
                NAM,
                dt.STT_DIEUTRI,
                dt.STT_BENHAN,
                dt.STT_DOTDIEUTRI,
                TU_TU_THUOC,
                NGAY,
                THANG,
                toathuoc.MABENHNHAN,
                toathuoc.KHOALAP,
                TRANG_THAI,
                KEY_QL,
                SOLOSANXUAT,
                NGAYSANXUAT,
                MALOAIVATTU,
                NHAPBANDAU,
                TT_THANHTOAN,
                NGAY_TATTOAN,
                'THUOC' KYHIEUNHOMBC,
                HAM_LUONG,
                DUONG_DUNG,
                SO_DANG_KY,
                dt.SOVAOVIEN,
                dt.SOVAOVIEN_DT,
                SONHAPKHOCHITIET,
                dt.ID_DIEUTRI,
                BANT,
                NGOAI_DANH_MUC,
                SO_LUONG,
                SO_LUONG_THUC_LINH,
                SO_TIEN_DA_TT_CK,
                MA_DICH_VU_VTYT,
                THUOC_GOIVATTUKTC,
                MA_DV_CHIDINH_VTYT,
                VATTU_CHUNGGOIVTYT,
                VATTU_STTGOIVTYT,
                NGAYGIO,
                nvl(ptchitiet.mavattu, 0) PHATHUOC
         from
             his_manager.NOITRU_DIEUTRI_HSBA_TEMP dt inner join
             his_manager.noitru_ct_toa_thuoc toathuoc on dt.dvtt = TOATHUOC.dvtt
                 and dt.sovaovien = toathuoc.SOVAOVIEN and dt.SOVAOVIEN_DT = toathuoc.SOVAOVIEN_DT
                 and dt.ID_DIEUTRI = TOATHUOC.ID_DIEUTRI
                                                     LEFT JOIN CMU_PHATHUOC_CHITIET ptchitiet on toathuoc.dvtt = ptchitiet.dvtt
                 and toathuoc.id_dieutri = ptchitiet.id_dieutri and
                                                                                                 toathuoc.sovaovien = ptchitiet.sovaovien and toathuoc.mavattu = ptchitiet.mavattu
         where dt.dvtt = p_dvtt
           and dt.STT_BENHAN = p_stt_benhan
           and (toathuoc.nghiep_vu IN ('noitru_toathuoc', 'ba_ngoaitru_toathuoc', 'noitru_toamienphi', 'noitru_toadongy', 'ba_ngoaitru_toadongy')
             --OR a.kyhieunhombc LIKE '%THUOC%'
             OR (v_thamso960615 = '1' and toathuoc.nghiep_vu = 'noitru_toadichvu' and toathuoc.KYHIEUNHOMBC LIKE 'THUOC%')
             OR (toathuoc.nghiep_vu = 'noitru_toaquaybanthuocbv' and toathuoc.KYHIEUNHOMBC LIKE 'THUOC%')
             )
           AND SO_LUONG > 0 and toathuoc.sovaovien = v_sovaovien and toathuoc.dvtt = p_dvtt
         -- AND (MALOAIVATTU LIKE 'TH%' OR MALOAIVATTU = 'DICHTRUYEN')
     ) a where PHATHUOC = 0
union all
select STT_TOATHUOC,
       STT_ORDER,
       dt.DVTT,
       MA_TOA_THUOC,
       0 MAKHOVATTU,
       0 MAVATTU,
       TEN_VAT_TU,
       HOAT_CHAT,
       DVT,
       NGHIEP_VU,
       0 DONGIA_BAN_BV,
       0 DONGIA_BAN_BH,
       0 THANHTIEN_THUOC,
       SO_NGAY_UONG,
       SANG_UONG,
       TRUA_UONG,
       CHIEU_UONG,
       TOI_UONG,
       1 XAC_NHAN,
       NGAY_RA_TOA,
       GHI_CHU_CT_TOA_THUOC,
       MA_BAC_SI_THEMTHUOC,
       0 DATHANHTOAN,
       CACH_SU_DUNG,
       NAM,
       dt.STT_DIEUTRI,
       dt.STT_BENHAN,
       dt.STT_DOTDIEUTRI,
       0 TU_TU_THUOC,
       NGAY,
       THANG,
       dt.MABENHNHAN,
       TO_CHAR(dt.KHOALAP),
       0 TRANG_THAI,
       null KEY_QL,
       null SOLOSANXUAT,
       null NGAYSANXUAT,
       'TH' MALOAIVATTU,
       0 NHAPBANDAU,
       0 TT_THANHTOAN,
       null NGAY_TATTOAN,
       'THUOC' KYHIEUNHOMBC,
       NULL HAM_LUONG,
       NULL DUONG_DUNG,
       NULL SO_DANG_KY,
       dt.SOVAOVIEN,
       dt.SOVAOVIEN_DT,
       0 SONHAPKHOCHITIET,
       dt.ID_DIEUTRI,
       0 BANT,
       0 NGOAI_DANH_MUC,
       SO_LUONG,
       SO_LUONG,
       0 SO_TIEN_DA_TT_CK,
       NULL MA_DICH_VU_VTYT,
       NULL THUOC_GOIVATTUKTC,
       NULL MA_DV_CHIDINH_VTYT,
       NULL VATTU_CHUNGGOIVTYT,
       NULL VATTU_STTGOIVTYT,
       NGAY_RA_TOA
from
    his_manager.NOITRU_DIEUTRI_HSBA_TEMP dt inner join
    his_manager.NOITRU_CT_TOA_THUOC_MUANGOAI toathuoc on dt.dvtt = TOATHUOC.dvtt
        and dt.sovaovien = toathuoc.SOVAOVIEN and dt.SOVAOVIEN_DT = toathuoc.SOVAOVIEN_DT
        and dt.ID_DIEUTRI = TOATHUOC.ID_DIEUTRI
where dt.dvtt = p_dvtt
  and dt.STT_BENHAN = p_stt_benhan
  AND SO_LUONG > 0 and toathuoc.sovaovien = v_sovaovien and toathuoc.dvtt = p_dvtt
ORDER BY STT_ORDER;

--NOITRU_CT_TOA_THUOC_TEMP
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 THUOC,
 THUOC_MUANGOAI,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt DVTT,
       p_stt_benhan STT_BENHAN,
       a.STT_DOTDIEUTRI stt_dotdieutri,
       a.stt_dieutri stt_dieutri,
       to_char(b.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       concat(case when a.NGHIEP_VU != 'noitru_toamuangoai' THEN  xmlcast(xmlagg(xmlelement(e,(CHR(10) || '- ' ||case
                                                                                                          when khangsinh.ngayuong IS NOT NULL THEN
                                                                                                              ' <style isBold="true">(' || khangsinh.ngayuong ||
                                                                                                              ') </style>'
                                                                                                          ELSE
                                                                                                              ''
           END || VT.tenvattu || '( ' || a.ham_luong || ' )'
           || CASE nvl(GHI_CHU_CT_TOA_THUOC,' ')
                  WHEN ' ' THEN
                      ''
                  ELSE '(' ||  GHI_CHU_CT_TOA_THUOC || ')' END
           ||CASE WHEN a.SANG_UONG = 0 THEN
                      ''
                  ELSE Concat(': Sáng: ', chuyen_thapphan_sang_phanso(a.SANG_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.TRUA_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Trưa: ', chuyen_thapphan_sang_phanso(a.TRUA_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.CHIEU_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Chiều: ', chuyen_thapphan_sang_phanso(a.CHIEU_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.TOI_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Tối: ', chuyen_thapphan_sang_phanso(a.TOI_UONG))
                                                                                                   END || decode(v_thamso_94300, '2', '', '(' || a.CACH_SU_DUNG || ')') -- Sang bo cach su dung khi tham so 94300 la 2
           || CMU_YLENHTHUOC_THEOGIO_GROUP(p_dvtt, a.id_dieutri, a.sovaovien, a.sovaovien_dt, a.stt_toathuoc)
           || ' (SL: ' || a.so_luong || ' ' || a.dvt || ')' -- Sang them so luong khi tham so 94300 la 2
           || chr(10))) order by A.STT_ORDER)
                                                                              .extract('//text()') as VARCHAR2(4000)) else ''  end,
              ''
       ) AS Y_LENH,
       concat( case when a.NGHIEP_VU = 'noitru_toamuangoai' THEN  xmlcast(xmlagg(xmlelement(e,('- ' ||case
                                                                                                          when khangsinh.ngayuong IS NOT NULL THEN
                                                                                                              ' <style isBold="true">(' || khangsinh.ngayuong ||
                                                                                                              ') </style>'
                                                                                                          ELSE
                                                                                                              ''
           END || a.TEN_VAT_TU
           || CASE nvl(GHI_CHU_CT_TOA_THUOC,' ')
                  WHEN ' ' THEN
                      ''
                  ELSE '(' ||  GHI_CHU_CT_TOA_THUOC || ')' END
           ||CASE WHEN a.SANG_UONG = 0 THEN
                      ''
                  ELSE Concat(': Sáng: ', chuyen_thapphan_sang_phanso(a.SANG_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.TRUA_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Trưa: ', chuyen_thapphan_sang_phanso(a.TRUA_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.CHIEU_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Chiều: ', chuyen_thapphan_sang_phanso(a.CHIEU_UONG))
                                                                                                   END ||CASE
                                                                                                             WHEN a.TOI_UONG = 0 THEN
                                                                                                                 ''
                                                                                                             ELSE
                                                                                                                 Concat(' Tối: ', chuyen_thapphan_sang_phanso(a.TOI_UONG))
                                                                                                   END || decode(v_thamso_94300, '2', '', '(' || a.CACH_SU_DUNG || ')')
           || CMU_YLENHTHUOC_THEOGIO_GROUP(p_dvtt, a.id_dieutri, a.sovaovien, a.sovaovien_dt, 'n'||a.stt_toathuoc)
           ||' (SL: ' || a.so_luong || ' ' || a.dvt || ')' -- Sang them so luong khi tham so 94300 la 2
           || chr(10))) order by A.STT_ORDER)
                                                                              .extract('//text()') as VARCHAR2(4000)) ELSE ''  END ,
               ''
       ) AS THUOC_MUANGOAI,
       b.ngaygiolap_tdt,
       c.ten_nhanvien_cd ten_nhanvien
FROM his_manager.NOITRU_CT_TOA_THUOC_TEMP a
         left join dc_tb_vattu vt
                   on a.dvtt = vt.dvtt
                       and vt.mavattu = a.mavattu
         left join (select toathuoc.MA_TOA_THUOC,
                           TOATHUOC.stt_order,
                           TOATHUOC.mavattu,
                           toathuoc.dvtt,
                           vattu.tenvattu,
                           toathuoc.ngay,
                           ngaythuoc.ngayuong
                    from NOITRU_DIEUTRI_HSBA_TEMP DIEUTRI
                             INNER JOIN
                         his_manager.NOITRU_CT_TOA_THUOC_TEMP toathuoc
                         ON TOATHUOC.DVTT = DIEUTRI.DVTT
                             AND TOATHUOC.ID_DIEUTRI = DIEUTRI.ID_DIEUTRI
                             AND toathuoc.SOVAOVIEN = DIEUTRI.SOVAOVIEN
                             AND TOATHUOC.SOVAOVIEN_DT = DIEUTRI.SOVAOVIEN_DT
                             inner join dc_tb_vattu vattu
                                        on TOATHUOC.dvtt = VATTU.dvtt
                                            and toathuoc.MAVATTU = vattu.MAVATTU
                             inner join DC_TB_NHOMVATTU nhom
                                        on nhom.manhomvattu = vattu.manhomvattu
                                            and nhom.dvtt = VATTU.dvtt
                             inner join (select NGAY, rownum ngayuong
                                         from (select DISTINCT TRUNC(ngaygiolap_tdt) NGAY
                                               from his_manager.NOITRU_CT_TOA_THUOC_TEMP toathuoc
                                                        inner join dc_tb_vattu vattu
                                                                   on TOATHUOC.dvtt = VATTU.dvtt
                                                                       and toathuoc.MAVATTU = vattu.MAVATTU
                                                        inner join DC_TB_NHOMVATTU nhom
                                                                   on nhom.manhomvattu =
                                                                      vattu.manhomvattu
                                                                       and nhom.dvtt = VATTU.dvtt
                                                                       and nhom.ghichu = 'KHANGSINH'
                                                        INNER JOIN NOITRU_DIEUTRI DIEUTRI
                                                                   ON TOATHUOC.DVTT = DIEUTRI.DVTT
                                                                       AND TOATHUOC.ID_DIEUTRI =
                                                                           DIEUTRI.ID_DIEUTRI
                                                                       AND TOATHUOC.SOVAOVIEN =
                                                                           DIEUTRI.SOVAOVIEN
                                                                       AND TOATHUOC.SOVAOVIEN_DT =
                                                                           DIEUTRI.SOVAOVIEN_DT
                                               where TOATHUOC.dvtt = p_dvtt
                                                 and TOATHUOC.SOVAOVIEN = v_svv
                                                 and TOATHUOC.sovaovien_dt = v_svv_dt
                                               order by TRUNC(ngaygiolap_tdt))) ngaythuoc
                                        on TRUNC(ngaygiolap_tdt) = ngaythuoc.ngay
                                            and nhom.ghichu = 'KHANGSINH'
                    where DIEUTRI.dvtt = p_dvtt AND DIEUTRI.STT_BENHAN = p_stt_benhan
                    --and TOATHUOC.SOVAOVIEN = v_svv
                    --and toathuoc.sovaovien_dt = v_svv_dt
                    order by toathuoc.ngay) KHANGSINH
                   ON KHANGSINH.MAVATTU = a.mavattu
                       and khangsinh.dvtt = a.dvtt
                       AND KHANGSINH.NGAY = A.NGAY
                       and a.stt_order = khangsinh.stt_order
                       and khangsinh.ma_toa_thuoc = a.ma_toa_thuoc, NOITRU_DIEUTRI_HSBA_TEMP b
         -- tung tv
                                                                        LEFT JOIN his_fw.dm_nhanvien_cd c
                                                                                  ON b.tdt_nguoilap = c.ma_nhanvien
--
WHERE a.stt_benhan = p_stt_benhan
  --AND a.stt_dotdieutri = p_stt_dotdieutri
  AND v_disable_auto = 0
  AND a.DVTT = p_dvtt
  AND b.stt_benhan = p_stt_benhan
  and ( a.nghiep_vu IN ('noitru_toathuoc', 'ba_ngoaitru_toathuoc', 'noitru_toamienphi', 'noitru_toadongy', 'ba_ngoaitru_toadongy')
    --OR a.kyhieunhombc LIKE '%THUOC%'
    OR (v_thamso960615 = '1' and a.nghiep_vu = 'noitru_toadichvu' and a.KYHIEUNHOMBC LIKE 'THUOC%')
    OR (a.nghiep_vu = 'noitru_toaquaybanthuocbv' and a.KYHIEUNHOMBC LIKE 'THUOC%')
    )
  --AND a.KYHIEUNHOMBC = 'THUOC'
  --and b.sovaovien = v_svv
  --and b.sovaovien_dt = v_svv_dt
  --AND b.stt_dotdieutri = p_stt_dotdieutri
  AND b.DVTT = p_dvtt
  AND a.sovaovien = b.sovaovien
  AND a.SOVAOVIEN_DT = b.SOVAOVIEN_DT and a.ID_DIEUTRI = b.ID_DIEUTRI
  AND a.DVTT = b.DVTT
  AND a.stt_dieutri = b.stt_dieutri

GROUP BY p_dvtt,
         p_stt_benhan,
         a.STT_DOTDIEUTRI,
         a.stt_dieutri,
         b.tdt_ylenh,
         b.ngaygiolap,
         b.ngaygiolap_tdt,
         c.ten_nhanvien_cd,
         a.NGHIEP_VU,
         b.ngaylap;

INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 THUOC_HOANTRA,
 ngaygio_date,
 ten_nhanvien)
select
    p_dvtt DVTT,
    p_stt_benhan STT_BENHAN,
    dtht.STT_DOTDIEUTRI stt_dotdieutri,
    dtht.stt_dieutri stt_dieutri,
    to_char(dtht.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI'),
    '<br/> Ngày y lệnh: ' || to_char(dt.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI')||
    LIStagg('<br/>- ' || vt.tenvattu  || '('|| vt.hoatchat ||')'
                || '(' || vt.hamluong  || ')'
                || ' (SL:' || ht.SO_LUONG || ' '|| vt.DVT|| ' )',
            '') WITHIN GROUP(ORDER BY NULL),
		 dtht.ngaygiolap_tdt,
     c.ten_nhanvien_cd ten_nhanvien
from NOITRU_DIEUTRI dtht
    inner join agg_nt_cttt_thuocsd_phieusai ht   on ht.dvtt = dtht.dvtt
    and ht.stt_benhan = dtht.stt_benhan and ht.stt_dotdieutri = dtht.stt_dotdieutri
    and ht.ID_DIEUTRI_HOANTRA = dtht.ID_DIEUTRI and ht.NGHIEP_VU = 'noitru_toathuoc'
    inner join NOITRU_DIEUTRI dt on ht.dvtt = dt.dvtt
    and ht.stt_benhan = dt.stt_benhan and ht.stt_dotdieutri = dt.stt_dotdieutri
    and ht.ID_DIEUTRI = dt.ID_DIEUTRI
    INNER JOIN DC_TB_VATTU vt on ht.dvtt = vt.dvtt and vt.mavattu = ht.mavattu
    LEFT JOIN his_fw.dm_nhanvien_cd c
    ON dtht.tdt_nguoilap = c.ma_nhanvien
where dtht.dvtt = p_Dvtt and
    dtht.stt_Benhan = p_stt_benhan

group by p_dvtt,
    p_stt_benhan,
    dtht.STT_DOTDIEUTRI,
    dtht.stt_dieutri,
    dt.ngaygiolap_tdt,
    dtht.ngaygiolap_tdt,
    C.ten_nhanvien_cd;

--- PHA THUOC
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 PHATHUOC,
 ngaygio_date,
 ten_nhanvien)
select
    DVTT,
    STT_BENHAN,
    stt_dotdieutri,
    stt_dieutri,
    ngaylap,
    '-' || GHICHU || XMLCAST(XMLAGG(XMLELEMENT(
            e,( '</br>' || Y_LENH)
                                    )  ORDER BY null ).extract('//text()') AS clob) YLENH,
    ngaygiolap_tdt,
    ten_nhanvien
from (
         select
             p_dvtt DVTT,
             p_stt_benhan STT_BENHAN,
             dtht.STT_DOTDIEUTRI stt_dotdieutri,
             dtht.stt_dieutri stt_dieutri,
             to_char(dtht.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') ngaylap,
             PHATHUOC.GHICHU,
             CMU_YLENHTHUOC_THEOGIO_GROUP(p_dvtt, phathuoc.id_dieutri, phathuoc.sovaovien, '-1', 'PHA_'||phathuoc.id_phathuoc) Y_LENH,
             dtht.ngaygiolap_tdt,
             c.ten_nhanvien_cd ten_nhanvien
         from NOITRU_DIEUTRI_HSBA_TEMP dtht
                  join CMU_PHATHUOC phathuoc on dtht.dvtt = phathuoc.dvtt
             and dtht.SOVAOVIEN = PHATHUOC.sovaovien
             and phathuoc.ID_DIEUTRI = dtht.ID_DIEUTRI
                  LEFT JOIN his_fw.dm_nhanvien_cd c
                            ON dtht.tdt_nguoilap = c.ma_nhanvien

         where dtht.dvtt = p_Dvtt and
             dtht.stt_Benhan = p_stt_benhan
     )
group by DVTT,
         STT_BENHAN,
         stt_dotdieutri,
         stt_dieutri,
         ngaylap,
         GHICHU ,
         ngaygiolap_tdt,
         ten_nhanvien;

--- END PHA THUOC

--- HUY THUOC
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 HUYYLENH_THUOC,
 ngaygio_date,
 ten_nhanvien)
select
    p_dvtt DVTT,
    p_stt_benhan STT_BENHAN,
    dtht.STT_DOTDIEUTRI stt_dotdieutri,
    dtht.stt_dieutri stt_dieutri,
    to_char(dtht.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI'),
    ' Bác sĩ chỉ định  ' || d.ten_nhanvien_cd|| ' ['|| 'Số phiếu: ' || dthuy.STT_DIEUTRI || ']:' ||
    LIStagg('<br/>- '
                || vt.tenvattu || '(' || vt.hoatchat || ')'
                ||  '(' || vt.hamluong || ')'
                ||  '(SL:' || PHATHUOC.SO_LUONG || ' ' || vt.DVT || ')'
                || chr(10),
            '') WITHIN GROUP(ORDER BY NULL),
		 dtht.ngaygiolap_tdt,
     c.ten_nhanvien_cd ten_nhanvien
from NOITRU_DIEUTRI_HSBA_TEMP dtht
    join HSBA_HUYYLTHUOC phathuoc on dtht.dvtt = phathuoc.dvtt
    and dtht.SOVAOVIEN = PHATHUOC.sovaovien
    and phathuoc.ID_DIEUTRI = dtht.ID_DIEUTRI

    join NOITRU_DIEUTRI_HSBA_TEMP dthuy on dthuy.dvtt = phathuoc.dvtt
    and dthuy.sovaovien = phathuoc.sovaovien and dthuy.id_dieutri = phathuoc.id_dieutri_huy

    join DC_TB_VATTU vt on phathuoc.dvtt = vt.dvtt and
    PHATHUOC.mavattu = vt.mavattu
    LEFT JOIN his_fw.dm_nhanvien_cd c
    ON dtht.tdt_nguoilap = c.ma_nhanvien

    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON dthuy.tdt_nguoilap = d.ma_nhanvien

where dtht.dvtt = p_Dvtt and
    dtht.stt_Benhan = p_stt_benhan

group by p_dvtt,
    p_stt_benhan,
    dtht.STT_DOTDIEUTRI,
    dtht.stt_dieutri,
    dtht.ngaygiolap_tdt,
    dthuy.STT_DIEUTRI,
    d.ten_nhanvien_cd,
    C.ten_nhanvien_cd;
--- END HUY THUOC

--- THUOC DA HUY
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 THUOC,
 ngaygio_date,
 ten_nhanvien)
select
    p_dvtt DVTT,
    p_stt_benhan STT_BENHAN,
    dtht.STT_DOTDIEUTRI stt_dotdieutri,
    dtht.stt_dieutri stt_dieutri,
    to_char(dtht.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI'),
    LIStagg('- '
                || vt.tenvattu || '(' || vt.hoatchat || ')'
                ||  '(' || vt.hamluong || ')'
                ||  '(SL:' || PHATHUOC.SO_LUONG || ' ' || vt.DVT || ')'
                || chr(10),
            '') WITHIN GROUP(ORDER BY NULL),
		 dtht.ngaygiolap_tdt,
     c.ten_nhanvien_cd ten_nhanvien
from NOITRU_DIEUTRI_HSBA_TEMP dtht
    join HSBA_HUYYLTHUOC phathuoc on dtht.dvtt = phathuoc.dvtt
    and dtht.SOVAOVIEN = PHATHUOC.sovaovien
    and phathuoc.ID_DIEUTRI_HUY = dtht.ID_DIEUTRI
    join DC_TB_VATTU vt on phathuoc.dvtt = vt.dvtt and
    PHATHUOC.mavattu = vt.mavattu
    LEFT JOIN his_fw.dm_nhanvien_cd c
    ON dtht.tdt_nguoilap = c.ma_nhanvien
where dtht.dvtt = p_Dvtt and
    dtht.stt_Benhan = p_stt_benhan
group by p_dvtt,
    p_stt_benhan,
    dtht.STT_DOTDIEUTRI,
    dtht.stt_dieutri,
    dtht.ngaygiolap_tdt,
    C.ten_nhanvien_cd;
--- END THUOC DA HUY

--- HUY Y LENH CLS
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 HUYYLENH_CLS,
 ngaygio_date,
 ten_nhanvien)
select
    p_dvtt DVTT,
    p_stt_benhan STT_BENHAN,
    dtht.STT_DOTDIEUTRI stt_dotdieutri,
    dtht.stt_dieutri stt_dieutri,
    to_char(dtht.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI'),
    '<br/> Bác sĩ chỉ định  ' || d.ten_nhanvien_cd|| ' ['|| 'Số phiếu: ' || dthuy.STT_DIEUTRI || ']:' ||
    LIStagg('<br/>- ' ||
            case when huycls.so_phieu like 'xn%' then nvl(xetnghiem.ten_hien_thi, xetnghiem.ten_xetnghiem)
                 when huycls.so_phieu like 'CD%' then nvl(cdha.ten_hien_thi, cdha.ten_cdha)
                 when huycls.so_phieu like 'dv%' then nvl(dv.ten_hien_thi, dv.TEN_DV)
                 else '' end
                || ' (' || huycls.SO_LUONG || ' Lần )' || '(' || to_char(huycls.thoigianylenh, 'DD/MM/YYYY HH24:MI')  || ')',
            '') WITHIN GROUP(ORDER BY NULL),
		 dtht.ngaygiolap_tdt,
     c.ten_nhanvien_cd ten_nhanvien
from NOITRU_DIEUTRI_HSBA_TEMP dtht
    JOIN HSBA_HUYYLCLS huycls on dtht.dvtt = huycls.dvtt
    and dtht.sovaovien = huycls.sovaovien and dtht.id_dieutri = huycls.id_dieutri

    join NOITRU_DIEUTRI_HSBA_TEMP dthuy on dthuy.dvtt = huycls.dvtt
    and dthuy.sovaovien = huycls.sovaovien and dthuy.id_dieutri = huycls.id_dieutri_huy

    left join CLS_XETNGHIEM xetnghiem on huycls.dvtt = xetnghiem.dvtt
    and huycls.ma_dv = xetnghiem.ma_xetnghiem and huycls.so_phieu like 'xn%'
    left join CLS_CDHA cdha on huycls.dvtt = cdha.dvtt
    and huycls.ma_dv = cdha.ma_cdha and huycls.so_phieu like 'CD%'
    left join DM_DICH_VU_KHAM dv on huycls.dvtt = dv.dvtt
    and huycls.ma_dv = dv.ma_dv and huycls.so_phieu like 'dv%'

    LEFT JOIN his_fw.dm_nhanvien_cd c
    ON dtht.tdt_nguoilap = c.ma_nhanvien

    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON dthuy.tdt_nguoilap = d.ma_nhanvien
where dtht.dvtt = p_Dvtt and
    dtht.stt_Benhan = p_stt_benhan

group by p_dvtt,
    p_stt_benhan,
    dtht.STT_DOTDIEUTRI,
    dtht.stt_dieutri,
    dthuy.STT_DIEUTRI,
    dtht.ngaygiolap_tdt,
    d.ten_nhanvien_cd,
    C.ten_nhanvien_cd;


--noitru_dieutri
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 Y_LENH,
 DIEN_BIEN_BENH,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       b.stt_dotdieutri,
       b.stt_dieutri,
       to_char(b.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,

       nvl(b.tdt_ylenh, ' ') AS Y_LENH,
       LIStagg(
           --
               CASE
                   WHEN nvl(b.MACH, ' ') = ' ' THEN
                       ' '
                   ELSE
                       ' - Mạch: ' || b.MACH || ' lần/phút,'
                   END ||
                   --
               CASE
                   WHEN nvl(b.NHIETDO, ' ') = ' ' THEN
                       ' '
                   ELSE
                       ' <br/> - Nhiệt độ: ' || b.NHIETDO || ' ᵒC,'
                   END ||
                   --
               CASE
                   WHEN nvl(b.HUYETAPTREN || '/' || b.HUYETAPDUOI, ' ') = '/' THEN
                       ' '
                   ELSE
                       ' <br/> - Huyết áp: ' || b.HUYETAPTREN || '/' || b.HUYETAPDUOI ||
                       ' mmHg,'
                   END ||
                   --
               CASE
                   WHEN nvl(b.NHIPTHO, ' ') = ' ' THEN
                       ' '
                   ELSE
                       ' <br/> - Nhịp thở: ' || b.NHIPTHO || ' lần/phút,'
                   END ||
                   --
               CASE
                   WHEN nvl(b.CANNANG, ' ') = ' ' or nvl(b.CHIEUCAO, ' ') = ' ' THEN
                       ' '
                   ELSE
                       ' <br/> - Cân nặng: ' || b.CANNANG || ' kg,' ||
                       ' <br/> - Chiều cao: ' || b.CHIEUCAO || ' cm,'
                   END ||
                   --BMI
               CASE
                   WHEN b.BMI != null and b.CHIEUCAO != null and b.CANNANG != null THEN
                   ' <br/> - BMI: ' || b.BMI || ','
                   ELSE
                   ' '
                   END ||
                   --SPO2
                   CASE
                   WHEN nvl(b.SPO2, ' ') = ' ' THEN
                   ' '
                   ELSE
                   ' <br/> - SPO2: ' || b.SPO2 || ' %,'
                   END ||
                   --Diễn biến bệnh
                   CASE
                   WHEN nvl(b.tdt_dienbienbenh, ' ') = ' ' THEN
                   ' '
                   ELSE
                   concat(' <br/>', b.tdt_dienbienbenh)
                   END ||
                   CASE WHEN b.TEN_BENHPHU is not null or b.ICD_DIEUTRI is not null or  b.TENICD_DIEUTRI is not null then
                   case
                   when p_dvtt in 96029 then
                   ' <br/> - Δ: '
                   else
                   ' <br/> - Chẩn đoán: '
                   end || b.ICD_DIEUTRI || ' - ' || b.TENICD_DIEUTRI || ';'||
                   b.TEN_BENHPHU || chr(10)
                   END ,
               ' - ') WITHIN GROUP(ORDER BY NULL) AS DIEN_BIEN_BENH,
           b.ngaygiolap_tdt,
           c.ten_nhanvien_cd ten_nhanvien
FROM NOITRU_DIEUTRI_HSBA_TEMP b
    LEFT JOIN his_fw.dm_nhanvien_cd c
ON b.tdt_nguoilap = c.ma_nhanvien
WHERE b.stt_benhan = p_stt_benhan
    --AND b.stt_dotdieutri = p_stt_dotdieutri
  AND b.DVTT = p_dvtt
--and b.sovaovien = v_svv
--and b.sovaovien_dt = v_svv_dt
--and (b.tdt_ylenh is not null or b.tdt_dienbienbenh is not null)
GROUP BY p_dvtt,
    p_stt_benhan,
    b.tdt_ylenh,
    b.STT_DOTDIEUTRI,
    b.stt_dieutri,
    b.ngaygiolap,
    b.ngaygiolap_tdt,
    b.ngaylap,
    c.ten_nhanvien_cd,
    b.stt_benhan,
    b.stt_dotdieutri,
    b.dvtt;

BEGIN
    DECLARE
v_finished NUMBER(10) DEFAULT 0;
      v_dieutri  VARCHAR2(100) DEFAULT '';

      -- declare cursor for employee dieutri
CURSOR dieutri_cursor IS
SELECT stt_dieutri, stt_dotdieutri
FROM NOITRU_DIEUTRI_HSBA_TEMP
WHERE stt_benhan = p_stt_benhan
  --AND stt_dotdieutri = p_stt_dotdieutri
  --and sovaovien = v_svv
  --and sovaovien_dt = v_svv_dt
  AND dvtt = p_dvtt
;
BEGIN

      -- declare NOT FOUND handler

OPEN dieutri_cursor;

<<get_dieutri>>
      LOOP

        FETCH dieutri_cursor
          INTO v_dieutri, v_stt_dotdieutri;
        IF dieutri_cursor%NOTFOUND THEN
          v_finished := 1;
END IF;

        IF v_finished = 1 THEN
          EXIT get_dieutri;
END IF;
        -- ----------------------------------------------

        IF v_dieutri > 0 THEN
          -- 1. Xét nghiệm
          INSERT INTO tem_noitru_todieutri_in
            (dvtt,
             stt_benhan,
             stt_dotdieutri,
             stt_dieutri,
             ngaygio,
             xn,
             ngaygio_date,
             ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.stt_dotdieutri,
       a.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       xmlcast(XMLAGG(xmlelement(e,'- ' || nvl(b.ten_hien_thi, b.ten_xetnghiem) || decode(a.ghichu_tenxn, null, '', '[' || a.ghichu_tenxn || ']') || ' (' ||
                                   a.SO_LUONG || ' lần )(' ||TO_CHAR(a.ngay_chi_dinh_ct, 'HH24:MI') || ')' || chr(10),
                                 '') order by rownum).EXTRACT('//text()') as clob),
       c.ngaygiolap_tdt,
       d.ten_nhanvien_cd ten_nhanvien
FROM  NOITRU_DIEUTRI_HSBA_TEMP c INNER JOIN
      his_manager.NOITRU_CD_XET_NGHIEM_CT a ON c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri
                                 LEFT JOIN his_fw.dm_nhanvien_cd d
                                           ON c.TDT_NGUOILAP = d.ma_nhanvien,
      his_manager.cls_xetnghiem           b

WHERE a.DVTT = p_dvtt
  --AND a.stt_dotdieutri = p_stt_dotdieutri
  AND a.stt_benhan = p_stt_benhan
  and c.stt_dotdieutri = v_stt_dotdieutri
  AND b.dvtt = p_dvtt and  a.sovaovien = v_sovaovien
  AND a.MA_XET_NGHIEM = b.ma_xetnghiem
  AND b.cap_xn = 1
  --AND c.stt_dotdieutri = p_stt_dotdieutri
  AND c.dvtt = p_dvtt
  AND c.stt_benhan = p_stt_benhan
  AND c.stt_dieutri = a.stt_dieutri
  AND a.stt_dieutri = v_dieutri and a.STT_DOTDIEUTRI = v_stt_dotdieutri
--and c.sovaovien = v_svv
--and c.sovaovien_dt = v_svv_dt
GROUP BY p_dvtt,
         p_stt_benhan,
         c.STT_DOTDIEUTRI,
         a.stt_dieutri,
         a.stt_dieutri,
         c.ngaygiolap,
         c.ngaygiolap_tdt,
         d.ten_nhanvien_cd,
         c.ngaylap;

-- 1. huy Xét nghiệm
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 xn,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.stt_dotdieutri,
       c.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       LIStagg('- ' || nvl(b.ten_hien_thi, b.ten_xetnghiem) || ' (' ||
               a.SO_LUONG || ' lần )(' ||TO_CHAR(a.thoigianylenh, 'HH24:MI') || ')' || chr(10),
               '') WITHIN GROUP(ORDER BY NULL),
															 c.ngaygiolap_tdt,
															 d.ten_nhanvien_cd ten_nhanvien
FROM  NOITRU_DIEUTRI_HSBA_TEMP c INNER JOIN
    his_manager.HSBA_HUYYLCLS a ON c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri_huy
    and a.sovaovien = c.sovaovien and a.so_phieu like 'xn%'
    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON c.TDT_NGUOILAP = d.ma_nhanvien,
    his_manager.cls_xetnghiem           b

WHERE a.DVTT = p_dvtt
  AND b.dvtt = p_dvtt and  a.sovaovien = v_sovaovien
  AND a.ma_dv = b.ma_xetnghiem
  AND b.cap_xn = 1
  AND c.dvtt = p_dvtt
  AND c.stt_dieutri = v_dieutri and c.STT_DOTDIEUTRI = v_stt_dotdieutri
GROUP BY p_dvtt,
    p_stt_benhan,
    c.STT_DOTDIEUTRI,
    c.stt_dieutri,
    c.ngaygiolap,
    c.ngaygiolap_tdt,
    d.ten_nhanvien_cd,
    c.ngaylap;

-- 2. Chẩn doán hình ảnh
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 cdha,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.stt_dotdieutri,
       a.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       LIStagg('- ' || nvl(ten_hien_thi, b.ten_cdha) || decode(a.ghichu_tencdha, null, '', '[' || a.ghichu_tencdha || ']') || ' (' ||
               cast(a.SO_LUONG AS NUMBER(18, 0)) || ' lần )' ||
               '(' ||TO_CHAR(a.ngay_chi_dinh_ct, 'HH24:MI') || ')' || chr(10),
               '') WITHIN GROUP(ORDER BY NULL),
															 c.ngaygiolap_tdt,
															 d.ten_nhanvien_cd ten_nhanvien
FROM NOITRU_DIEUTRI_HSBA_TEMP   c
    inner join his_manager.NOITRU_CD_CDHA_CHI_TIET a  ON
    c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri
    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON c.tdt_nguoilap = d.ma_nhanvien,
    his_manager.cls_cdha                b


WHERE a.DVTT = p_dvtt
    --AND a.stt_dotdieutri = p_stt_dotdieutri
  AND a.stt_benhan = p_stt_benhan
  AND v_disable_auto = 0
  and c.STT_DOTDIEUTRI = v_stt_dotdieutri
  AND b.dvtt = p_dvtt
  AND a.MA_CDHA = b.ma_CDHA and  a.sovaovien = v_sovaovien
  and a.STT_DOTDIEUTRI = v_stt_dotdieutri
  AND c.dvtt = p_dvtt
  AND c.stt_benhan = p_stt_benhan
  AND c.stt_dieutri = a.stt_dieutri
  AND a.stt_dieutri = v_dieutri
--and c.sovaovien = v_svv
--and c.sovaovien_dt = v_svv_dt
GROUP BY p_dvtt,
    p_stt_benhan,
    c.STT_DOTDIEUTRI,
    a.stt_dieutri,
    c.ngaygiolap,
    c.ngaygiolap_tdt,
    d.ten_nhanvien_cd,
    c.ngaylap;


-- 2. huy CDHA
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 cdha,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.stt_dotdieutri,
       c.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       LIStagg('- ' || nvl(b.ten_hien_thi, b.ten_cdha) || ' (' ||
               a.SO_LUONG || ' lần )(' ||TO_CHAR(a.thoigianylenh, 'HH24:MI') || ')' || chr(10),
               '') WITHIN GROUP(ORDER BY NULL),
															 c.ngaygiolap_tdt,
															 d.ten_nhanvien_cd ten_nhanvien
FROM  NOITRU_DIEUTRI_HSBA_TEMP c INNER JOIN
    his_manager.HSBA_HUYYLCLS a ON c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri_huy
    and a.sovaovien = c.sovaovien and a.so_phieu like 'CD%'
    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON c.TDT_NGUOILAP = d.ma_nhanvien,
    his_manager.cls_cdha           b

WHERE a.DVTT = p_dvtt
  AND b.dvtt = p_dvtt and  a.sovaovien = v_sovaovien
  AND a.ma_dv = b.MA_CDHA
  AND c.dvtt = p_dvtt
  AND c.stt_dieutri = v_dieutri and c.STT_DOTDIEUTRI = v_stt_dotdieutri
GROUP BY p_dvtt,
    p_stt_benhan,
    c.STT_DOTDIEUTRI,
    c.ngaygiolap, c.stt_dieutri,
    c.ngaygiolap_tdt,
    d.ten_nhanvien_cd,
    c.ngaylap;

-- 3. Thủ thuật / Phẫu thuật
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 ttpt,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.STT_DOTDIEUTRI,
       a.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
    /*LIStagg('- ' || nvl(b.ten_hien_thi, b.TEN_DV) || decode(a.ghichu_tenttpt, null, '', '[' || a.ghichu_tenttpt || ']') || ' (' ||
            cast(a.SO_LUONG AS NUMBER(18, 0)) || ' lần )' ||
            '(' ||TO_CHAR(a.ngay_chi_dinh_ct, 'HH24:MI') || ')' || chr(10),
            '') WITHIN GROUP(ORDER BY NULL),*/
       TO_CLOB(
               XMLSERIALIZE(
                       CONTENT XMLAGG(
                    XMLELEMENT(
                       "x",
                       '- ' || NVL(b.ten_hien_thi, b.TEN_DV) ||
                       DECODE(a.ghichu_tenttpt, NULL, '', '[' || a.ghichu_tenttpt || ']') || ' (' ||
                       TO_CHAR(a.SO_LUONG) || ' lần )' || '(' || TO_CHAR(a.ngay_chi_dinh_ct, 'HH24:MI') || ')' || CHR(10)
                    ) ORDER BY NULL
                ) AS CLOB
               )
       ),
       c.ngaygiolap_tdt,
       d.ten_nhanvien_cd ten_nhanvien
FROM NOITRU_DIEUTRI_HSBA_TEMP c inner join
     his_manager.noitru_cd_dichvu_CT a  ON c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri
                                LEFT JOIN his_fw.dm_nhanvien_cd d
                                          ON c.tdt_nguoilap = d.ma_nhanvien,
     his_manager.dm_dich_vu_kham     b


WHERE a.DVTT = p_dvtt
  and c.STT_DOTDIEUTRI = v_stt_dotdieutri
  AND a.stt_benhan = p_stt_benhan
  AND v_disable_auto = 0
  AND v_disable_auto_ttpt = 0
  and a.STT_DOTDIEUTRI = v_stt_dotdieutri
  AND b.dvtt = p_dvtt
  AND a.MA_DV = b.MA_DV and  a.sovaovien = v_sovaovien
-- AND c.stt_dotdieutri = p_stt_dotdieutri
  AND c.dvtt = p_dvtt
  AND c.stt_benhan = p_stt_benhan
  AND c.stt_dieutri = a.stt_dieutri
  AND a.stt_dieutri = v_dieutri
  and b.LOAI_DV in ('TT', 'PT', 'VLTL', 'THO_OXY')
--and c.sovaovien = v_svv
--and c.sovaovien_dt = v_svv_dt
GROUP BY p_dvtt,
         p_stt_benhan,
         c.STT_DOTDIEUTRI,
         a.stt_dieutri,
         c.ngaygiolap,
         c.ngaygiolap_tdt,
         d.ten_nhanvien_cd,
         c.ngaylap;

-- 3. huy TTPT
INSERT INTO tem_noitru_todieutri_in
(dvtt,
 stt_benhan,
 stt_dotdieutri,
 stt_dieutri,
 ngaygio,
 ttpt,
 ngaygio_date,
 ten_nhanvien)
SELECT p_dvtt,
       p_stt_benhan,
       c.stt_dotdieutri,
       c.stt_dieutri,
       to_char(c.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       LIStagg('- ' || nvl(b.ten_hien_thi, b.ten_dv) || ' (' ||
               a.SO_LUONG || ' lần )(' ||TO_CHAR(a.thoigianylenh, 'HH24:MI') || ')' || chr(10),
               '') WITHIN GROUP(ORDER BY NULL),
															 c.ngaygiolap_tdt,
															 d.ten_nhanvien_cd ten_nhanvien
FROM  NOITRU_DIEUTRI_HSBA_TEMP c INNER JOIN
    his_manager.HSBA_HUYYLCLS a ON c.dvtt = a.dvtt and c.id_dieutri = a.id_dieutri_huy
    and a.sovaovien = c.sovaovien and a.so_phieu like 'dv%'
    LEFT JOIN his_fw.dm_nhanvien_cd d
    ON c.TDT_NGUOILAP = d.ma_nhanvien,
    his_manager.dm_dich_vu_kham           b

WHERE a.DVTT = p_dvtt
  AND b.dvtt = p_dvtt and  a.sovaovien = v_sovaovien
  AND a.ma_dv = b.MA_dv
  AND c.dvtt = p_dvtt
  AND c.stt_dieutri = v_dieutri and c.STT_DOTDIEUTRI = v_stt_dotdieutri
GROUP BY p_dvtt,
    p_stt_benhan,
    c.STT_DOTDIEUTRI,
    c.ngaygiolap, c.stt_dieutri,
    c.ngaygiolap_tdt,
    d.ten_nhanvien_cd,
    c.ngaylap;

END IF;
        -- ----------------------------------------------
END LOOP get_dieutri;

CLOSE dieutri_cursor;

END;

begin
select pb.TEN_PHONG, gb.TENGIUONG into v_phongbenh, v_sogiuong
from CMU_HSBA_PHONGBENH pb
         inner join CMU_HSBA_PHONGBENH_GB gb on pb.dvtt = GB.dvtt
    and pb."ID" = gb.MA_PHONG
where gb.dvtt = p_Dvtt and GB.SOVAOVIEN = v_sovaovien
  and rownum <=1;
exception
	when no_data_found then v_phongbenh:=null;v_sogiuong:=null;
end;



OPEN cur FOR
WITH RemoveDuplicates AS (
    SELECT t.*,
           ROW_NUMBER() OVER (
               PARTITION BY t.stt_dieutri, t.dvtt, t.stt_benhan, t.stt_dotdieutri
               ORDER BY t.ngaygio DESC
           ) as rn
    FROM (
            select a.stt_dieutri,
                   a.dvtt,
                   a.stt_benhan,
                   a.stt_dotdieutri,
                   a.ngaygio,

                   case when xn is not null then '<p class="text-success font-weight-bold mb-0">Xét nghiệm: </p><br/>'|| xn else null end	 ||
                   case when CDHA is not null then '<p class="text-primary font-weight-bold mb-0">Chẩn đoán hình ảnh: </p><br/>'|| CDHA else '' end ||
                   case when TTPT is not null then '<p class="text-danger font-weight-bold mb-0">Thủ thuật/phẫu thuật: </p><br/>'|| TTPT else null end ||
                   case when PHATHUOC is not null then '<p class="text-info font-weight-bold mb-0">Pha thuốc:</p><br/>'|| PHATHUOC else NULL end ||
                   case when truyenmau.dvtt is not null then '<p class="text-warning font-weight-bold mb-0">Truyền máu:</p>' else '' end ||
                      (SELECT LISTAGG(
                        CASE
                            WHEN dvtt IS NOT NULL THEN
                                ' - ' ||
                                SOLUONG || ' ml' ||
                                CASE
                                    WHEN tocdo IS NOT NULL THEN
                                        '; Tốc độ truyền: ' || tocdo || ' ' ||
                                        CASE
                                            WHEN loai = '0' THEN 'giọt/phút'
                                            ELSE 'ml/giờ'
                                        END
                                END ||
                                ' (' || TO_CHAR(NGAYTRUYEN, 'HH24:MI') || ')'
                            ELSE ''
                        END,
                        CHR(10)
                    ) WITHIN GROUP (ORDER BY NGAYTRUYEN)
                    FROM CMU_YLENHTRUYENMAU
                    WHERE dvtt = a.dvtt
                    AND sovaovien = a.sovaovien
                    AND id_dieutri = a.id_dieutri) ||
                   case when THUOC is not null then '<p class="font-weight-bold mb-0" style="color:#FF7F50">Thuốc/dịch truyền: </p><br/>'|| THUOC else '' end ||
                   case when
                            CMU_YLENHKHACTDT_THEOGIO_GET(p_dvtt, a.ID_DIEUTRI, a.SOVAOVIEN, a.SOVAOVIEN_DT) is not null
                            then '<p class="font-weight-bold mb-0" style="color:#236868">Thuốc/dịch truyền (thực hiện tiếp tục): </p>'||
                                 CMU_YLENHKHACTDT_THEOGIO_GET(p_dvtt, a.ID_DIEUTRI, a.SOVAOVIEN, a.SOVAOVIEN_DT) else '' end ||
                   case when THUOC_MUANGOAI is not null then '<p class="font-weight-bold mb-0" style="color:#8B008B">Thuốc mua ngoài: </p><br/>'|| THUOC_MUANGOAI else '' end ||
                   case when THUOC_HOANTRA is not null then '<p class="font-weight-bold mb-0" style="color:#FFB6C1">Hoàn trả thuốc:</p>'|| THUOC_HOANTRA else '' end ||
                   case when HUYYLENH_CLS is not null then '<p class="font-weight-bold mb-0" style="color:#0000CD">Hủy thực hiện y lệnh cận lâm sàng: </p>'|| HUYYLENH_CLS else '' end ||
                   case when HUYYLENH_THUOC is not null then '<p class="font-weight-bold mb-0" style="color:#808000">Hủy y lệnh thuốc: </p><br/>'|| HUYYLENH_THUOC else '' end ||
                         case when cmu_getthongtin_moikham(P_DVTT,p_stt_benhan, a.id_dieutri ) is not null then '<p class="font-weight-bold mb-0" style="color:#123271">Y lệnh mời khám: </p>'|| cmu_getthongtin_moikham(P_DVTT,p_stt_benhan, a.id_dieutri ) else '' end ||
                         case when CMU_GETTHONGTINTHOMAY_TDT(P_DVTT,p_stt_benhan,a.stt_dotdieutri, a.stt_dieutri, a.id_dieutri ) is not null
                                then '<p class="font-weight-bold mb-0" style="color:#682106">Thở máy: </p>'|| CMU_GETTHONGTINTHOMAY_TDT(P_DVTT,p_stt_benhan,a.stt_dotdieutri, a.stt_dieutri, a.id_dieutri ) else '' end ||
                         case when Y_LENH is not null then '<p class="font-weight-bold mb-0" style="color:#6B8E23">Y lệnh: </p>' || Y_LENH else '' end ||
                         case when CMU_GETNGUNGTRUYENDICH_TDT(P_DVTT,p_stt_benhan,a.stt_dotdieutri, a.stt_dieutri, a.id_dieutri) is not null
                            then '<p class="font-weight-bold mb-0" style="color:#9b7e27">Ngưng truyền dịch: </p>' || CMU_GETNGUNGTRUYENDICH_TDT(P_DVTT,p_stt_benhan,a.stt_dotdieutri, a.stt_dieutri, a.id_dieutri) end AS Y_LENH,
                   DIEN_BIEN_BENH ||
                         case when moikham.dvtt is not null then 'Y lệnh mời khám: <br/>'|| CMU_GETYLENH_MOIKHAM(P_DVTT, moikham.id) end  AS DIEN_BIEN_BENH,
                   a.ngaygio_date,
                   a.ten_nhanvien,
                   a.TRANG_THAI,
                   a.TEN_PHONGBAN,
                   a.KHOALAP,
                   a.SOVAOVIEN,
                   a.SOVAOVIEN_DT,
                   a.ID_DIEUTRI,
                   a.NGUOITAO,
                   ten_nhanvien_cd,
                   SIGNKCB.KEYSIGN,
                   nvl(pt.ID,0) PHAUTHUAT,
                   v_phongbenh PHONGBENH,
                   v_sogiuong SOGIUONG,
                   a.TDT_NGUOILAP,
                         nvl(moikham.dvtt,0) YLENHMOIKHAM
            from (SELECT TEM.stt_dieutri,
                         TEM.dvtt,
                         TEM.stt_benhan,
                         TEM.stt_dotdieutri,
                         TEM.ngaygio,
                         xmlcast(XMLAGG(xmlelement(e,xn, '') order by rownum).EXTRACT('//text()') as clob)   XN,
                         --'' XN,
                         LIStagg(cdha, '') WITHIN GROUP(ORDER BY NULL) cdha,
                      xmlcast(XMLAGG(xmlelement(e,ttpt, '') order by rownum).EXTRACT('//text()') as clob) ttpt,
                      LIStagg(THUOC, '') WITHIN GROUP(ORDER BY NULL) THUOC,
                      LIStagg(THUOC_MUANGOAI, '') WITHIN GROUP(ORDER BY NULL) THUOC_MUANGOAI,
                      LIStagg(THUOC_HOANTRA, '') WITHIN GROUP(ORDER BY NULL) THUOC_HOANTRA,
                      LIStagg(Y_LENH, '') WITHIN GROUP(ORDER BY NULL) Y_LENH,
                      LIStagg(HUYYLENH_CLS, '') WITHIN GROUP(ORDER BY NULL) HUYYLENH_CLS,
                      xmlcast(XMLAGG(xmlelement(e,PHATHUOC, '') order by rownum).EXTRACT('//text()') as clob)  PHATHUOC,
                      LIStagg(HUYYLENH_THUOC, '') WITHIN GROUP(ORDER BY NULL) HUYYLENH_THUOC,
                      LIStagg(DIEN_BIEN_BENH,'') WITHIN GROUP(ORDER BY DATE_CREATED) DIEN_BIEN_BENH,
                      TEM.ngaygio_date,
                      TEM.ten_nhanvien,
                      TOATHUOC.TRANG_THAI,
                      PBAN.TEN_PHONGBAN,
                      toathuoc.KHOALAP,
                      toathuoc.SOVAOVIEN,
                      toathuoc.SOVAOVIEN_DT,
                      toathuoc.ID_DIEUTRI,
                      dt.NGUOITAO,
                      nv.ten_nhanvien ten_nhanvien_cd,
                      DT.TDT_NGUOILAP
                  FROM tem_noitru_todieutri_in TEM INNER JOIN
                      NOITRU_TOA_THUOC TOATHUOC ON TEM.DVTT = TOATHUOC.DVTT AND TEM.STT_BENHAN = TOATHUOC.STT_BENHAN
                      AND TEM.STT_DOTDIEUTRI = TOATHUOC.STT_DOTDIEUTRI AND TEM.STT_DIEUTRI = TOATHUOC.STT_DIEUTRI
                      INNER JOIN HIS_FW.DM_PHONGBAN PBAN ON TOATHUOC.KHOALAP = PBAN.MA_PHONGBAN AND PBAN.MA_DONVI = P_DVTT
                      INNER JOIN NOITRU_DIEUTRI_HSBA_TEMP dt on TOATHUOC.dvtt = dt.dvtt
                      and TOATHUOC.id_dieutri = DT.ID_DIEUTRI and dt.sovaovien = toathuoc.sovaovien and
                      dt.sovaovien_dt = toathuoc.sovaovien_dt
                      AND DT.SOVAOVIEN = toathuoc.sovaovien and dt.sovaovien_Dt = toathuoc.sovaovien_dt

                      left join HIS_FW.dm_nhanvien nv on dt.nguoitao = nv.ma_nhanvien
                  WHERE TEM.dvtt = p_dvtt
                    AND TEM.stt_benhan = p_stt_benhan
                  GROUP BY TEM.dvtt,
                      TEM.stt_benhan,
                      TEM.stt_dotdieutri,
                      TEM.ngaygio,
                      TEM.ngaygio_date,
                      TEM.ten_nhanvien,
                      TEM.stt_dieutri,
                      TOATHUOC.TRANG_THAI,
                      PBAN.TEN_PHONGBAN,
                      toathuoc.KHOALAP,
                      toathuoc.SOVAOVIEN,
                      toathuoc.SOVAOVIEN_DT,
                      toathuoc.ID_DIEUTRI,
                      dt.NGUOITAO,
                      nv.ten_nhanvien,
                      dt.tdt_nguoilap
                  ORDER BY ngaygio_date DESC) a
                     left join SMARTCA_SIGNED_KCB SIGNKCB ON SIGNKCB.DVTT = a.dvtt
                and signkcb.sovaovien = a.sovaovien and signkcb.sovaovien_dt = signkcb.sovaovien_dt
                and signkcb.ky_hieu_phieu = 'TODIEUTRI_NOITRU' and signkcb.ID_DIEUTRI = a.ID_DIEUTRI
                and signkcb.so_phieu_dv = to_char(a.ID_DIEUTRI) AND signkcb.STATUS IN (0,2)
                     LEFT JOIN CMU_YLENHTRUYENMAU truyenmau on a.dvtt = truyenmau.dvtt and a.sovaovien = truyenmau.sovaovien
                and a.id_dieutri = truyenmau.id_dieutri
                     left join CMU_LANPHAUTHUAT pt on a.dvtt = pt.dvtt and a.sovaovien = pt.sovaovien and a.id_dieutri = pt.id_dieutri
                             left join CMU_HSBA_MOIKHAM moikham on a.dvtt = moikham.dvtt and a.stt_benhan = moikham.stt_benhan and a.id_dieutri = moikham.id_dieutri
            /*where trim(nvl(XN, ' ') || nvl(CDHA, ' ') || nvl(TTPT, ' ') ||
                       nvl(Y_LENH, ' ') || nvl(DIEN_BIEN_BENH, ' ')) is not null*/
            order by NGAYGIO_DATE DESC
        ) t
    ) SELECT * FROM RemoveDuplicates WHERE rn = 1 ORDER BY ngaygio_date DESC;
return cur;
END;
END;